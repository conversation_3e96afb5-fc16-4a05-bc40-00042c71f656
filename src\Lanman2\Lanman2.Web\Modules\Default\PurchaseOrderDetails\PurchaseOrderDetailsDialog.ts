import { EmployeesRow, PurchaseOrderDetailsForm, PurchaseOrderDetailsRow, PurchaseOrderDetailsService, PurchaseOrdersRow, PurchaseOrdersService, ShippingAddressesRow } from '@/ServerTypes/Default';
import { Decorators, EditorUtils, EntityDialog, indexOf, serviceRequest, toId } from '@serenity-is/corelib';

@Decorators.registerClass('Lanman2.Default.PurchaseOrderDetailsDialog')
export class PurchaseOrderDetailsDialog extends EntityDialog<PurchaseOrderDetailsRow, any> {
    protected getFormKey() { return PurchaseOrderDetailsForm.formKey; }
    protected getRowDefinition() { return PurchaseOrderDetailsRow; }
    protected getService() { return PurchaseOrderDetailsService.baseUrl; }

    protected form = new PurchaseOrderDetailsForm(this.idPrefix);

    constructor() {
        super();

        this.form.IsDiscoveryComplete.change(e => {
            const purchaseOrderId = toId(this.form.PurchaseOrderId.value);
            if (purchaseOrderId != null) {
                PurchaseOrdersRow.getLookupAsync().then(lookup => {
                    const purchaseOrder = lookup.itemById[purchaseOrderId];
                    purchaseOrder.IsDiscoveryComplete = this.form.IsDiscoveryComplete.value;
                    serviceRequest(PurchaseOrdersService.Methods.Update, {
                        EntityId: purchaseOrderId,
                        Entity: purchaseOrder
                    });
                });
            }
        });

        this.form.AssignedOwnerEmployeeId.changeSelect2(e => {
            const employeeId = toId(this.form.AssignedOwnerEmployeeId.value);
            if (employeeId) {
                EmployeesRow.getLookupAsync().then(lookup => {
                    const employee = lookup.itemById[employeeId];
                    this.form.ShippingAttention.value = employee.FullName;
                });
            }
        });

        this.form.ShippingAddressesId.change(e => {
            const shippingAddressId = toId(this.form.ShippingAddressesId.value);
            if (shippingAddressId != null) {
                ShippingAddressesRow.getLookupAsync().then(lookup => {
                    const item = lookup.itemById[shippingAddressId];
                    this.form.ShippingAddressesAddress.value = item.Address;
                    this.form.ShippingAddressesCityName.value = item.CityName;
                    this.form.ShippingAddressesStateName.value = item.StateName;
                    this.form.ShippingAddressesZIPCode.value = item.ZipCode;
                });
            }
        });
    }

    protected updateInterface() {
        super.updateInterface();


        if (this.entity.PurchaseOrderNo) {
            EditorUtils.setReadonly(this.form.ItemTypeName.element, true);
            EditorUtils.setReadonly(this.form.ItemCode.element, true);
            EditorUtils.setReadonly(this.form.SerialNumber.element, true);
            EditorUtils.setReadonly(this.form.PurchaseOrderNo.element, true);
            EditorUtils.setReadonly(this.form.ItemName.element, true);
            EditorUtils.setReadonly(this.form.ShippingAttention.element, true);
            EditorUtils.setReadonly(this.form.ShippingAddressesAddress.element, true);
            EditorUtils.setReadonly(this.form.ShippingAddressesCityName.element, true);
            EditorUtils.setReadonly(this.form.ShippingAddressesStateName.element, true);
            EditorUtils.setReadonly(this.form.ShippingAddressesZIPCode.element, true);
        }
    }

    protected getToolbarButtons() {
        let buttons = super.getToolbarButtons();

        buttons.splice(indexOf(buttons, x => x.title == "Delete"), 1);

        return buttons;
    }

    protected afterLoadEntity() {
        super.afterLoadEntity();

        this.set_dialogTitle("Lease Item (" + this.entity.SerialNumber + ")");

        this.form.ReturnDate.getGridField().toggle(false);
        this.form.ReturnEmailDate.getGridField().toggle(false);
        this.form.ReturnTrackingNumber.getGridField().toggle(false);
        this.form.IsItemBuyOut.getGridField().toggle(false);
        this.form.ItemBuyOutAmount.getGridField().toggle(false);
        this.form.ItemDiskWipeDate.getGridField().toggle(false);
        this.form.IsItemDisabled.getGridField().toggle(false);
        this.form.ItemDisabledDate.getGridField().toggle(false);
        this.form.IsReturnComplete.getGridField().toggle(false);
    }

}
