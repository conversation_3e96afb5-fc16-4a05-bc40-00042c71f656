{"version": 3, "sources": ["../../../../../../Modules/Membership/Account/SignUp/SignUpPage.tsx"], "sourcesContent": ["/** @jsxImportSource jsx-dom */\r\nimport { SignUpForm, SignUpRequest } from \"@/ServerTypes/Membership\";\r\nimport { SignUpResponse } from \"@/ServerTypes/Membership/SignUpResponse\";\r\nimport { Texts } from \"@/ServerTypes/Texts\";\r\nimport { PropertyPanel } from \"@serenity-is/corelib\";\r\nimport { informationDialog, resolveUrl, serviceCall } from \"@serenity-is/corelib\";\r\nimport { AccountPanelTitle } from \"../AccountPanelTitle\";\r\n\r\nexport default function pageInit(opt: any) {\r\n    new SignUpPanel($('#SignUpPanel'), opt);\r\n}\r\n\r\nconst myTexts = Texts.Forms.Membership.SignUp;\r\n\r\nclass SignUpPanel extends PropertyPanel<SignUpRequest, any> {\r\n\r\n    protected getFormKey() { return SignUpForm.formKey; }\r\n\r\n    private form: SignUpForm;\r\n\r\n    constructor(container: JQuery, opt: any) {\r\n        super(container, opt);\r\n\r\n        this.form = new SignUpForm(this.idPrefix);\r\n\r\n        this.form.Email.element.attr(\"autocomplete\", \"off\");\r\n        this.form.Password.element.attr(\"autocomplete\", \"new-password\");\r\n\r\n        this.form.ConfirmEmail.addValidationRule(this.uniqueName, e => {\r\n            if (this.form.ConfirmEmail.value !== this.form.Email.value) {\r\n                return Texts.Validation.EmailConfirm;\r\n            }\r\n        });\r\n\r\n        this.form.ConfirmPassword.addValidationRule(this.uniqueName, e => {\r\n            if (this.form.ConfirmPassword.value !== this.form.Password.value) {\r\n                return Texts.Validation.PasswordConfirmMismatch;\r\n            }\r\n        });\r\n    }\r\n\r\n    submitClick() {\r\n        if (!this.validateForm()) {\r\n            return;\r\n        }\r\n\r\n        var request = this.propertyGrid.save();\r\n        delete request.ConfirmEmail;\r\n        delete request.ConfirmPassword;\r\n\r\n        serviceCall({\r\n            url: resolveUrl('~/Account/SignUp'),\r\n            request: request,\r\n            onSuccess: (response: SignUpResponse) => {\r\n                informationDialog(myTexts.Success, () => {\r\n                    window.location.href = resolveUrl('~/');\r\n                });\r\n            }\r\n        });\r\n    }\r\n\r\n    renderContents() {\r\n        const id = this.useIdPrefix();\r\n        this.element.empty().append(<>\r\n            <AccountPanelTitle />\r\n\r\n            <div class=\"s-Panel p-4\">\r\n                <h5 class=\"text-center my-4\">{myTexts.FormTitle}</h5>\r\n                <p id={id.FormInfo} class=\"text-center\">{myTexts.FormInfo}</p>\r\n\r\n                <form id={id.Form} action=\"\" autoComplete=\"off\">\r\n                    <input autoComplete=\"false\" name=\"hidden\" type=\"text\" style=\"display:none;\" />\r\n                    <div id={id.PropertyGrid}></div>\r\n                    <div class=\"px-field\">\r\n                        <button id={id.SubmitButton} type=\"submit\" class=\"btn btn-primary my-4 w-100\"\r\n                            onClick={e => { e.preventDefault(); this.submitClick(); }}>\r\n                            {myTexts.SubmitButton}\r\n                        </button>\r\n                    </div>\r\n                </form>\r\n            </div>\r\n        </>)\r\n    }\r\n}\r\n"], "mappings": "4MAIA,IAAAA,EAA8B,SAC9BA,EAA2D,SAG5C,SAARC,EAA0BC,EAAU,CACvC,IAAIC,EAAY,EAAE,cAAc,EAAGD,CAAG,CAC1C,CAFwBE,EAAAH,EAAA,YAIxB,IAAMI,EAAUC,EAAM,MAAM,WAAW,OAEjCC,EAAN,MAAMA,UAAoB,eAAkC,CAMxD,YAAYC,EAAmBN,EAAU,CACrC,MAAMM,EAAWN,CAAG,EAEpB,KAAK,KAAO,IAAIO,EAAW,KAAK,QAAQ,EAExC,KAAK,KAAK,MAAM,QAAQ,KAAK,eAAgB,KAAK,EAClD,KAAK,KAAK,SAAS,QAAQ,KAAK,eAAgB,cAAc,EAE9D,KAAK,KAAK,aAAa,kBAAkB,KAAK,WAAYC,GAAK,CAC3D,GAAI,KAAK,KAAK,aAAa,QAAU,KAAK,KAAK,MAAM,MACjD,OAAOJ,EAAM,WAAW,YAEhC,CAAC,EAED,KAAK,KAAK,gBAAgB,kBAAkB,KAAK,WAAYI,GAAK,CAC9D,GAAI,KAAK,KAAK,gBAAgB,QAAU,KAAK,KAAK,SAAS,MACvD,OAAOJ,EAAM,WAAW,uBAEhC,CAAC,CACL,CAvBU,YAAa,CAAE,OAAOG,EAAW,OAAS,CAyBpD,aAAc,CACV,GAAK,KAAK,aAAa,EAIvB,KAAIE,EAAU,KAAK,aAAa,KAAK,EACrC,OAAOA,EAAQ,aACf,OAAOA,EAAQ,mBAEf,eAAY,CACR,OAAK,cAAW,kBAAkB,EAClC,QAASA,EACT,UAAYC,GAA6B,IACrC,qBAAkBP,EAAQ,QAAS,IAAM,CACrC,OAAO,SAAS,QAAO,cAAW,IAAI,CAC1C,CAAC,CACL,CACJ,CAAC,EACL,CAEA,gBAAiB,CACb,IAAMQ,EAAK,KAAK,YAAY,EAC5B,KAAK,QAAQ,MAAM,EAAE,OAAOC,EAAAC,EAAA,CACxB,UAAAD,EAACE,EAAA,EAAkB,EAEnBF,EAAC,OAAI,MAAM,cACP,UAAAA,EAAC,MAAG,MAAM,mBAAoB,SAAAT,EAAQ,UAAU,EAChDS,EAAC,KAAE,GAAID,EAAG,SAAU,MAAM,cAAe,SAAAR,EAAQ,SAAS,EAE1DS,EAAC,QAAK,GAAID,EAAG,KAAM,OAAO,GAAG,aAAa,MACtC,UAAAC,EAAC,SAAM,aAAa,QAAQ,KAAK,SAAS,KAAK,OAAO,MAAM,gBAAgB,EAC5EA,EAAC,OAAI,GAAID,EAAG,aAAc,EAC1BC,EAAC,OAAI,MAAM,WACP,SAAAA,EAAC,UAAO,GAAID,EAAG,aAAc,KAAK,SAAS,MAAM,6BAC7C,QAASH,GAAK,CAAEA,EAAE,eAAe,EAAG,KAAK,YAAY,CAAG,EACvD,SAAAL,EAAQ,aACb,EACJ,GACJ,GACJ,GACJ,CAAG,CACP,CACJ,EArE4DD,EAAAG,EAAA,eAA5D,IAAMJ,EAANI", "names": ["import_corelib", "pageInit", "opt", "SignUpPanel", "__name", "myTexts", "Texts", "_SignUpPanel", "container", "SignUpForm", "e", "request", "response", "id", "jsx", "Fragment", "AccountPanelTitle"]}