﻿import { CostCentersColumns, CostCentersRow, CostCentersService } from '@/ServerTypes/Default';
import { Decorators, EntityGrid } from '@serenity-is/corelib';
import { CostCentersDialog } from './CostCentersDialog';

@Decorators.registerClass('Lanman2.Default.CostCentersGrid')
export class CostCentersGrid extends EntityGrid<CostCentersRow, any> {
    protected getColumnsKey() { return CostCentersColumns.columnsKey; }
    protected getDialogType() { return CostCentersDialog; }
    protected getRowDefinition() { return CostCentersRow; }
    protected getService() { return CostCentersService.baseUrl; }

}