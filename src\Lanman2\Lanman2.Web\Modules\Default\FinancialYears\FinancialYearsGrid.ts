﻿import { FinancialYearsColumns, FinancialYearsRow, FinancialYearsService } from '@/ServerTypes/Default';
import { Decorators, EntityGrid } from '@serenity-is/corelib';
import { FinancialYearsDialog } from './FinancialYearsDialog';

@Decorators.registerClass('Lanman2.Default.FinancialYearsGrid')
export class FinancialYearsGrid extends EntityGrid<FinancialYearsRow, any> {
    protected getColumnsKey() { return FinancialYearsColumns.columnsKey; }
    protected getDialogType() { return FinancialYearsDialog; }
    protected getRowDefinition() { return FinancialYearsRow; }
    protected getService() { return FinancialYearsService.baseUrl; }

}