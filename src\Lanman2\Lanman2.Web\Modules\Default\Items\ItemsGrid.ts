import { ItemsColumns, ItemsRow, ItemsService } from '@/ServerTypes/Default';
import { Decorators, EntityGrid } from '@serenity-is/corelib';
import { ItemsDialog } from './ItemsDialog';

@Decorators.registerClass('Lanman2.Default.ItemsGrid')
export class ItemsGrid extends EntityGrid<ItemsRow, any> {
    protected getColumnsKey() { return ItemsColumns.columnsKey; }
    protected getDialogType() { return ItemsDialog; }
    protected getRowDefinition() { return ItemsRow; }
    protected getService() { return ItemsService.baseUrl; }
  
}