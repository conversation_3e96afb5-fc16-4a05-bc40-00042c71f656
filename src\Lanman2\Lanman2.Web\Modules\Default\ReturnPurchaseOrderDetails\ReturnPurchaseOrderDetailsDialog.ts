import { PurchaseOrderDetailsForm } from '@/ServerTypes/Default';
import { Decorators, EditorUtils } from '@serenity-is/corelib';
import { PurchaseOrderDetailsDialog } from '../PurchaseOrderDetails/PurchaseOrderDetailsDialog';

@Decorators.registerClass('Lanman2.Default.ReturnPurchaseOrderDetailsDialog')
export class ReturnPurchaseOrderDetailsDialog extends PurchaseOrderDetailsDialog {
    protected form = new PurchaseOrderDetailsForm(this.idPrefix);

    constructor() {
        super();

        this.form.IsReturnComplete.change(e => {
            const isReturnComplete = this.form.IsReturnComplete.value;
            if (isReturnComplete && !(this.form.ReturnDate.value ?? false)) {
                this.form.ReturnDate.valueAsDate = new Date();
                this.form.ItemDisabledDate.valueAsDate = new Date();
                this.form.IsItemDisabled.value = isReturnComplete;
            }
        });
    }

    protected afterLoadEntity() {
        super.afterLoadEntity();

        this.form.SerialNumber.getGridField().toggle(true);
        EditorUtils.setReadonly(this.form.SerialNumber.element, true);
        EditorUtils.setReadonly(this.form.ShippingAddressesId.element, true);
        this.form.ShippingAddressesId.getGridField().find(".inplace-create").remove();


        this.form.PurchaseOrderId.getGridField().toggle(false);
        this.form.ItemTypeName.getGridField().toggle(false);
        this.form.ItemCode.getGridField().toggle(false);
        this.form.PurchaseOrderNo.getGridField().toggle(false);
        this.form.ItemName.getGridField().toggle(false);
        this.form.AssignedOwnerEmployeeId.getGridField().toggle(false);
        this.form.CostCenterId.getGridField().toggle(false);
        this.form.ShippingAddressesId.getGridField().toggle(true);
        this.form.ShippingAttention.getGridField().toggle(false);
        this.form.ShippingAddressesAddress.getGridField().toggle(true);

        this.form.ShippingAddressesZIPCode.getGridField().toggle(true);
        this.form.ReplacementNotes.getGridField().toggle(false);
        this.form.ReplaceEmailSentOnDate.getGridField().toggle(false);
        this.form.ReplaceEmailResponseReceivedDate.getGridField().toggle(false);
        this.form.ReplacementTypeId.getGridField().toggle(false);
        this.form.IsDiscoveryComplete.getGridField().toggle(false);

        this.form.ReturnDate.getGridField().toggle(true);
        this.form.ReturnEmailDate.getGridField().toggle(true);
        this.form.ReturnTrackingNumber.getGridField().toggle(true);
        this.form.IsItemBuyOut.getGridField().toggle(true);
        this.form.ItemBuyOutAmount.getGridField().toggle(true);
        this.form.ItemDiskWipeDate.getGridField().toggle(true);
        this.form.IsItemDisabled.getGridField().toggle(true);
        this.form.ItemDisabledDate.getGridField().toggle(true);
        this.form.IsReturnComplete.getGridField().toggle(true);

        this.set_dialogTitle("Return Item Info");
    }
}