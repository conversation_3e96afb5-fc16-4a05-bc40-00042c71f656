import { ItemsForm, ItemsRow, ItemsService } from '@/ServerTypes/Default';
import { alertDialog, Decorators, EntityDialog, reloadLookup, serviceRequest } from '@serenity-is/corelib';

@Decorators.registerClass('Lanman2.Default.ItemsDialog')
export class ItemsDialog extends EntityDialog<ItemsRow, any> {
    protected getFormKey() { return ItemsForm.formKey; }
    protected getRowDefinition() { return ItemsRow; }
    protected getService() { return ItemsService.baseUrl; }

    protected form = new ItemsForm(this.idPrefix);

    constructor() {
        super();

        this.form.IsDefault.change(a => {
            if (this.form.IsDefault.value  && this.form.IsDiscontinued.value) {
                alertDialog('Default cannot be set to a discontinued item!');
                return false;
            }
        });
    }

    onSaveSuccess(response) {
        super.onSaveSuccess(response);
        reloadLookup(ItemsRow.lookupKey);
    }

    validateBeforeSave() {

        let isValidate = super.validateBeforeSave();

        if (this.form.IsDiscontinued.value  && this.form.IsDefault.value) {
            alertDialog('Default cannot be set to a discontinued item!');
            return false;
        }
        else {
            if (this.form.IsDefault.value) {
                let currentDefaultItem = ItemsRow.getLookup().items.find(b => b.ItemTypeName === this.form.ItemTypeId.selectedItem.ItemTypeName
                    && b.IsDefault);

                if (currentDefaultItem) {
                    currentDefaultItem.IsDefault = false;

                    serviceRequest(ItemsService.Methods.Update, {
                        EntityId: currentDefaultItem.ItemId,
                        Entity: currentDefaultItem
                    }, (currentDefaultItemUpdateResponse) => {
                    });
                }
            }
            return isValidate;
        }
    }
}