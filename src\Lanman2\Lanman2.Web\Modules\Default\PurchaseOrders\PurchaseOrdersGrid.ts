import { PurchaseOrdersColumns, PurchaseOrdersRow, PurchaseOrdersService } from '@/ServerTypes/Default';
import { Criteria, Decorators, EntityGrid, StringEditor } from '@serenity-is/corelib';
import { PurchaseOrdersDialog } from './PurchaseOrdersDialog';
import { ReportHelper } from "@serenity-is/extensions";


const fld = PurchaseOrdersRow.Fields;

@Decorators.registerClass('Lanman2.Default.PurchaseOrdersGrid')
export class PurchaseOrdersGrid extends EntityGrid<PurchaseOrdersRow, any> {
    protected getColumnsKey() { return PurchaseOrdersColumns.columnsKey; }
    protected getDialogType() { return PurchaseOrdersDialog; }
    protected getRowDefinition() { return PurchaseOrdersRow; }
    protected getService() { return PurchaseOrdersService.baseUrl; }


    protected setCriteriaParameter() {
        super.setCriteriaParameter();

        const criteria = this.view.params.Criteria;

        this.view.params.Criteria = Criteria.and(
            criteria,
            [[fld.PurchaseOrderNo], '!=', '']
        );
    }

    protected getColumns() {
        let columns = super.getColumns();
        

        columns.splice(0, 0, {
            id: 'Print Invoice',
            field: null,
            name: '',
            cssClass: 'align-center',
            format: ctx => '<a class="inline-action print-invoice" title="PO">' +
                '<i class="fa fa-file-pdf-o text-red"></i></a>',
            width: 36,
            minWidth: 36,
            maxWidth: 36
        });

        return columns;
    }

    protected onClick(e: JQueryEventObject, row: number, cell: number) {
        super.onClick(e, row, cell);

        if (e.isDefaultPrevented())
            return;

        let item = this.itemAt(row);

        let target = $(e.target);

        // if user clicks "i" element, e.g. icon
        if (target.parent().hasClass('inline-action'))
            target = target.parent();

        if (target.hasClass('inline-action')) {
            e.preventDefault();

            if (target.hasClass('print-invoice')) {
                ReportHelper.execute({
                    reportKey: 'PurchaseOrderReport',
                    params: {
                        OrderID: item.PurchaseOrderId
                    }
                });
            }
        }
    }

    protected getQuickFilters() {
        let quickFilters = super.getQuickFilters();

        const currentDate = new Date();
        const month = String(currentDate.getMonth() + 1).padStart(2, '0');
        const year = String(currentDate.getFullYear());
        const formattedDate = `${month}/${year}`;

        let purchaseOrderSimpleCreatedDateFilter = quickFilters.find(x => x.field === fld.PurchaseOrderSimpleCreatedDate);


        purchaseOrderSimpleCreatedDateFilter && (purchaseOrderSimpleCreatedDateFilter.init = (w: StringEditor) => {
            w.value = formattedDate
        });

        return quickFilters;
    }
}
