import { ItemsRow, NewPurchaseOrderDetailsForm, PurchaseOrderDetailsRow } from '@/ServerTypes/Default';
import { Authorization, Decorators, EditorUtils, indexOf, toId } from '@serenity-is/corelib';
import { GridEditorDialog } from '@serenity-is/extensions';


@Decorators.registerClass('Lanman2.Default.PurchaseOrderDetailsEditDialog')
export class PurchaseOrderDetailsEditDialog extends GridEditorDialog<PurchaseOrderDetailsRow> {
    protected getFormKey() { return NewPurchaseOrderDetailsForm.formKey; }
    protected getLocalTextPrefix() { return PurchaseOrderDetailsRow.localTextPrefix; }

    protected form = new NewPurchaseOrderDetailsForm(this.idPrefix);

    constructor() {
        super();
        
        this.form.ItemId.changeSelect2(e => {
            const itemId = toId(this.form.ItemId.value);
            if (itemId != null) {
                ItemsRow.getLookupAsync().then(lookup => {
                    const item = lookup.itemById[itemId];
                    if (item) {
                        this.form.UnitPrice.value = item.UnitPrice;
                    }
                });
            }
        })
    }

    protected afterLoadEntity() {
        super.afterLoadEntity();
        this.set_dialogTitle("PO Item");

        if (Authorization.hasPermission("Administration:General")) {
            EditorUtils.setReadonly(this.element.find('[name="ItemTypeId"]'), false);
            EditorUtils.setReadonly(this.element.find('[name="ItemId"]'), false);
            EditorUtils.setReadonly(this.element.find('[name="Quantity"]'), false);
            EditorUtils.setReadonly(this.element.find('[name="UnitPrice"]'), false);
        }
        else
        {
            EditorUtils.setReadonly(this.element.find('[name="ItemTypeId"]'), true);
            EditorUtils.setReadonly(this.element.find('[name="ItemId"]'), true);
            EditorUtils.setReadonly(this.element.find('[name="Quantity"]'), true);
            EditorUtils.setReadonly(this.element.find('[name="UnitPrice"]'), true);
        }
    }

    protected getToolbarButtons() {
        let buttons = super.getToolbarButtons();

        buttons.splice(indexOf(buttons, x => x.title == "Undelete"), 1);
        buttons.splice(indexOf(buttons, x => x.title == "Localization"), 1);
        buttons.splice(indexOf(buttons, x => x.title == "Clone"), 1);

        if (Authorization.hasPermission("Administration:POVendor")) {

            buttons.splice(indexOf(buttons, x => x.title == "Delete"), 1);
        }

        return buttons;
    }
}