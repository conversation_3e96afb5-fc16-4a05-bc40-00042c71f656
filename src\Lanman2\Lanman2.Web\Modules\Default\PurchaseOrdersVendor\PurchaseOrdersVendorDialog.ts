import { CostCentersRow, PurchaseOrderDetailsRow, PurchaseOrderDetailsService, PurchaseOrdersService, PurchaseOrdersVendorForm, PurchaseOrdersVendorRow, PurchaseOrdersVendorService } from '@/ServerTypes/Default';
import { Decorators, EditorUtils, EntityDialog, indexOf, reloadLookup, serviceRequest, toId } from '@serenity-is/corelib';

@Decorators.registerClass('Lanman2.Default.PurchaseOrdersVendorDialog')
export class PurchaseOrdersVendorDialog extends EntityDialog<PurchaseOrdersVendorRow, any> {
    protected getFormKey() { return PurchaseOrdersVendorForm.formKey; }
    protected getRowDefinition() { return PurchaseOrdersVendorRow; }
    protected getService() { return PurchaseOrdersVendorService.baseUrl; }

    protected form = new PurchaseOrdersVendorForm(this.idPrefix);
    oldPurchaseOrderId: number;

    constructor(oldPurchaseOrderIdParam: number) {
        super();
        this.oldPurchaseOrderId = oldPurchaseOrderIdParam;
    }

    protected afterLoadEntity() {
        super.afterLoadEntity();

        this.form.IsReOrderInfoGatheringComplete.getGridField().toggle(false);
        this.form.IsReplacementPOComplete.getGridField().toggle(false);

        if (!this.entity.PurchaseOrderNo) {
            let costCenterId = toId(this.entity.CostCenterId);
            if (costCenterId) {
                let dateNow = new Date();
                CostCentersRow.getLookupAsync().then(a => {
                    let costCenter = a.itemById[costCenterId].CostCenterName;
                    let month = (dateNow.getMonth() + 1).toString().padStart(2, '0');
                    let day = dateNow.getDate().toString().padStart(2, '0');

                    let costCenterPO = costCenter + "-" + month + day + dateNow.getFullYear().toString().substring(2);
                    PurchaseOrdersVendorRow.getLookupAsync().then(b => {
                        let purchaseOrderCount = b.items.filter(x => x.PurchaseOrderNo?.startsWith(costCenterPO)).length;

                        this.form.PurchaseOrderNo.value = costCenterPO + "-" + (purchaseOrderCount + 1);
                    });
                });
            }
        }
    }

    protected updateInterface() {
        super.updateInterface();

        EditorUtils.setReadonly(this.form.ShippingAddressesAddress.element, true);
        EditorUtils.setReadonly(this.form.ShippingAddressesCityName.element, true);
        EditorUtils.setReadonly(this.form.ShippingAddressesStateName.element, true);
        EditorUtils.setReadonly(this.form.ShippingAddressesZIPCode.element, true);
        EditorUtils.setReadonly(this.form.PurchaseOrderNo.element, true);
        EditorUtils.setReadonly(this.form.AssignedOwnerEmployeeId.element, true);
        EditorUtils.setReadonly(this.form.CostCenterId.element, true);
        EditorUtils.setReadonly(this.form.ShippingAttention.element, true);
        EditorUtils.setReadonly(this.form.ShippingAddressesId.element, true);
        EditorUtils.setReadonly(this.form.PurchaseOrderCreatedDate.element, true);
        EditorUtils.setReadonly(this.form.PurchaseOrderEmailSentOnDate.element, true);
        EditorUtils.setReadonly(this.form.PurchaseOrderNotes.element, true);
        EditorUtils.setReadonly(this.form.VendorId.element, true);
        EditorUtils.setReadonly(this.form.IsReOrderInfoGatheringComplete.element, true);
        EditorUtils.setReadonly(this.form.IsReplacementPOComplete.element, true);
    }

    // Refactored onSaveSuccess method with async/await and helper methods
    protected async onSaveSuccess(response) {
        reloadLookup(PurchaseOrdersVendorRow.lookupKey);
        reloadLookup(PurchaseOrderDetailsRow.lookupKey);
        const entityId = response.EntityId;

        if (this.oldPurchaseOrderId > 0) {
            try {
                await this.handleOldPurchaseOrderUpdate(entityId);
            } catch (error) {
                console.error('Error in onSaveSuccess:', error);
            }
        }
    }

    private async handleOldPurchaseOrderUpdate(entityId: number) {
        const purchaseOrderLookup = await PurchaseOrdersVendorRow.getLookupAsync();
        const oldPurchaseOrder = purchaseOrderLookup.itemById[this.oldPurchaseOrderId];

        if (this.form.IsReOrderInfoGatheringComplete && !this.form.IsReOrderInfoGatheringComplete.element[0].hidden) {
            oldPurchaseOrder.IsReOrderInfoGatheringComplete = this.form.IsReOrderInfoGatheringComplete?.value;
        }

        if (this.form.IsReplacementPOComplete && !this.form.IsReplacementPOComplete.element[0].hidden) {
            oldPurchaseOrder.IsReplacementPOComplete = this.form.IsReplacementPOComplete?.value;
        }

        await this.updatePurchaseOrderDetails(entityId);
        await this.updateReplacementPurchaseOrder(entityId);
        await this.updateOldPurchaseOrder(oldPurchaseOrder);
    }

    private async updatePurchaseOrderDetails(entityId: number) {
        const purchaseOrderDetailsLookup = await PurchaseOrderDetailsRow.getLookupAsync();
        const purchaseOrderDetails = purchaseOrderDetailsLookup.items.filter(x =>
            x.PurchaseOrderId === this.oldPurchaseOrderId &&
            (x.ItemTypeName === 'Desktop' || x.ItemTypeName === 'Laptop'));

        if (purchaseOrderDetails.length > 0 &&
            (!purchaseOrderDetails[0].ReplacementPurchaseOrderId || purchaseOrderDetails[0].ReplacementPurchaseOrderId < 0)) {

            purchaseOrderDetails[0].ReplacementPurchaseOrderId = entityId;

            await new Promise<void>((resolve, reject) => {
                serviceRequest(PurchaseOrderDetailsService.Methods.Update, {
                    EntityId: purchaseOrderDetails[0].PurchaseOrderDetailId,
                    Entity: purchaseOrderDetails[0]
                }, () => resolve());
            });
        }
    }

    private async updateReplacementPurchaseOrder(entityId: number) {
        const purchaseOrderLookup = await PurchaseOrdersVendorRow.getLookupAsync();
        const replacementPurchaseOrder = purchaseOrderLookup.itemById[entityId];
        replacementPurchaseOrder.IsReOrderInfoGatheringComplete = false;
        replacementPurchaseOrder.IsReplacementPOComplete = false;

        await new Promise<void>((resolve, reject) => {
            serviceRequest(PurchaseOrdersService.Methods.Update, {
                EntityId: entityId,
                Entity: replacementPurchaseOrder
            }, () => resolve());
        });
    }

    private async updateOldPurchaseOrder(oldPurchaseOrder: PurchaseOrdersVendorRow) {
        await new Promise<void>((resolve, reject) => {
            serviceRequest(PurchaseOrdersVendorService.Methods.Update, {
                EntityId: this.oldPurchaseOrderId,
                Entity: oldPurchaseOrder
            }, () => resolve());
        });
    }

    getToolbarButtons() {
        let buttons = super.getToolbarButtons();

        buttons.splice(indexOf(buttons, x => x.title == "Delete"), 1);

        return buttons;
    }
}