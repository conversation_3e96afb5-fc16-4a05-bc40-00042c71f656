import { ItemTypesRow, PurchaseOrderDetailsColumns, PurchaseOrderDetailsRow, PurchaseOrderDetailsService, PurchaseOrdersRow } from '@/ServerTypes/Default';
import { Criteria, Decorators, EntityGrid, first, indexOf, StringEditor, ToolButton} from '@serenity-is/corelib';
import { PurchaseOrderDetailsDialog } from '../PurchaseOrderDetails/PurchaseOrderDetailsDialog';
import { RaiseNewPODialog } from './RaiseNewPODialog';

const fld = PurchaseOrderDetailsRow.Fields;

@Decorators.registerClass('Lanman2.Default.RaiseNewPOGrid')
export class RaiseNewPOGrid extends EntityGrid<PurchaseOrderDetailsRow, any> {
    protected getColumnsKey() { return PurchaseOrderDetailsColumns.columnsKey; }
    protected getDialogType() { return PurchaseOrderDetailsDialog; }
    protected getRowDefinition() { return PurchaseOrderDetailsRow; }
    protected getService() { return PurchaseOrderDetailsService.baseUrl; }
    
    protected getInitialTitle() { return 'New POs' }

    protected getQuickFilters() {
        let quickFilters = super.getQuickFilters();

        const currentDate = new Date();
        const month = String(currentDate.getMonth() + 1).padStart(2, '0');
        const year = String(currentDate.getFullYear());
        const formattedDate = `${month}/${year}`;

        let leaseExpiryDateShortFilter = quickFilters.find(x => x.field === fld.LeaseExpiryDateShort);
        leaseExpiryDateShortFilter && (leaseExpiryDateShortFilter.init = (w: StringEditor) => {
            w.value = formattedDate
        });

        return quickFilters.filter(x => x.field === fld.IsReplacementPOComplete
            || x.field === fld.LeaseExpiryDateShort);
    }

    protected setCriteriaParameter() {
        super.setCriteriaParameter();

        const itemTypeDesktop = ItemTypesRow.getLookup().items.find(x => x.ItemTypeName === 'Desktop');
        const itemTypeLaptop = ItemTypesRow.getLookup().items.find(x => x.ItemTypeName === 'Laptop');

        if (itemTypeDesktop && itemTypeLaptop) {

            const itemTypeDesktopId = itemTypeDesktop.ItemTypeId;
            const itemTypeLaptopId = itemTypeLaptop.ItemTypeId;
            const desktopLaptopCriteria = Criteria.or([[fld.ItemTypeId], '=', itemTypeDesktopId], [[fld.ItemTypeId], '=', itemTypeLaptopId]);

            const criteria = this.view.params.Criteria;
            const serialNumberCriteria = [[fld.SerialNumber], '!=', ''];
            const isReOrderInfoGatheringCriteria = [[fld.IsReOrderInfoGatheringComplete], '=', true];
            const replacementTypeCriteria = [[fld.ReplacementType], '=', 'Order New'];

            this.view.params.Criteria = Criteria.and(
                criteria,
                serialNumberCriteria,
                replacementTypeCriteria,
                isReOrderInfoGatheringCriteria,
                desktopLaptopCriteria
            );
        }
    }

    protected onClick(e: Event, row: number, cell: number) {

        e.preventDefault();

        let item = this.itemAt(row);
        let purchaseOrderDialog = new RaiseNewPODialog(item.PurchaseOrderId);

        this.initDialog(purchaseOrderDialog);

        if (!item.ReplacementPurchaseOrderId) {

            purchaseOrderDialog.loadEntityAndOpenDialog(<PurchaseOrdersRow>{
                PurchaseOrderNo: item.PurchaseOrderNo,
                AssignedOwnerEmployeeId: item.AssignedOwnerEmployeeId,
                CostCenterId: item.CostCenterId,
                ShippingAttention: item.ShippingAttention,
                ShippingAddressesId: item.ShippingAddressesId,
                ShippingAddressesAddress: item.ShippingAddressesAddress,
                ShippingAddressesCityName: item.ShippingAddressesCityName,
                ShippingAddressesStateName: item.ShippingAddressesStateName,
                ShippingAddressesZIPCode: item.ShippingAddressesZIPCode,
                PurchaseOrderCreatedDate: item.PurchaseOrderCreatedDate,
                VendorId: item.VendorId
            });
        }
        else {
            (PurchaseOrdersRow.getLookupAsync()).then(c => {
                let replacementPurchaseOrder = c.itemById[item.ReplacementPurchaseOrderId];

                PurchaseOrderDetailsRow.getLookupAsync().then(i => {                   
                    let replacementPODetails = i.items.filter(f => f.PurchaseOrderId === replacementPurchaseOrder.PurchaseOrderId);

                    purchaseOrderDialog.loadEntityAndOpenDialog(<PurchaseOrdersRow>{
                        PurchaseOrderId: item.ReplacementPurchaseOrderId,
                        PurchaseOrderNo: replacementPurchaseOrder.PurchaseOrderNo,
                        PurchaseOrderCreatedDate: replacementPurchaseOrder.PurchaseOrderCreatedDate,
                        AssignedOwnerEmployeeId: replacementPurchaseOrder.AssignedOwnerEmployeeId,
                        CostCenterId: replacementPurchaseOrder.CostCenterId,
                        ShippingAttention: replacementPurchaseOrder.ShippingAttention,
                        ShippingAddressesId: replacementPurchaseOrder.ShippingAddressesId,
                        ShippingAddressesAddress: replacementPurchaseOrder.ShippingAddressesAddress,
                        ShippingAddressesCityName: replacementPurchaseOrder.ShippingAddressesCityName,
                        ShippingAddressesStateName: replacementPurchaseOrder.ShippingAddressesStateName,
                        ShippingAddressesZIPCode: replacementPurchaseOrder.ShippingAddressesZIPCode,
                        VendorId: replacementPurchaseOrder.VendorId,
                        IsReOrderInfoGatheringComplete: item.IsReOrderInfoGatheringComplete,

                        PurchaseOrderDetailsList: replacementPODetails
                    });
                });
            });
        }
    }

    protected getButtons(): ToolButton[] {
        let buttons = super.getButtons();

        buttons.splice(indexOf(buttons, x => x.action == "add"), 1);

        return buttons;
    }

    protected getColumns() {
        let columns = super.getColumns();

        first(columns, x => x.field == "SerialNumber").name = "Old Serial No.";
        first(columns, x => x.field == "PurchaseOrderNo").name = "Old PO No.";
        
        return columns;
    }
}