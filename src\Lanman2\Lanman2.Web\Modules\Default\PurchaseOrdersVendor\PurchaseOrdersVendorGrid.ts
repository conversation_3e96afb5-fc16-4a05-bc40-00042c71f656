import { PurchaseOrdersRow, PurchaseOrdersVendorColumns, PurchaseOrdersVendorRow, PurchaseOrdersVendorService } from '@/ServerTypes/Default';
import { Criteria, Decorators, EntityGrid, indexOf, StringEditor } from '@serenity-is/corelib';
import { ReportHelper } from "@serenity-is/extensions";
import { PurchaseOrdersVendorDialog } from './PurchaseOrdersVendorDialog';


const fld = PurchaseOrdersRow.Fields;

@Decorators.registerClass('Lanman2.Default.PurchaseOrdersVendorGrid')
export class PurchaseOrdersVendorGrid extends EntityGrid<PurchaseOrdersVendorRow, any> {
    protected getColumnsKey() { return PurchaseOrdersVendorColumns.columnsKey; }
    protected getDialogType() { return PurchaseOrdersVendorDialog; }
    protected getRowDefinition() { return PurchaseOrdersVendorRow; }
    protected getService() { return PurchaseOrdersVendorService.baseUrl; }

    protected setCriteriaParameter() {
        super.setCriteriaParameter();

        const criteria = this.view.params.Criteria;

        this.view.params.Criteria = Criteria.and(
            criteria,
            [[fld.PurchaseOrderNo], '!=', '']
        );
    }

    protected getButtons() {
        let buttons = super.getButtons();

        buttons.splice(indexOf(buttons, x => x.title == "New Purchase Orders"), 1);
        return buttons;
    }

    protected getColumns() {
        let columns = super.getColumns();
        


        return columns;
    }

    protected onClick(e: JQueryEventObject, row: number, cell: number) {
        super.onClick(e, row, cell);

        if (e.isDefaultPrevented())
            return;

        let item = this.itemAt(row);

        let target = $(e.target);

        // if user clicks "i" element, e.g. icon
        if (target.parent().hasClass('inline-action'))
            target = target.parent();

        if (target.hasClass('inline-action')) {
            e.preventDefault();

            if (target.hasClass('print-invoice')) {
                ReportHelper.execute({
                    reportKey: 'PurchaseOrderReport',
                    params: {
                        OrderID: item.PurchaseOrderId
                    }
                });
            }
        }
    }

    protected getQuickFilters() {
        let quickFilters = super.getQuickFilters();

        const currentDate = new Date();
        const month = String(currentDate.getMonth() + 1).padStart(2, '0');
        const year = String(currentDate.getFullYear());
        const formattedDate = `${month}/${year}`;

        let purchaseOrderSimpleCreatedDateFilter = quickFilters.find(x => x.field === fld.PurchaseOrderSimpleCreatedDate);


        purchaseOrderSimpleCreatedDateFilter && (purchaseOrderSimpleCreatedDateFilter.init = (w: StringEditor) => {
            w.value = formattedDate
        });

        return quickFilters;
    }
}
