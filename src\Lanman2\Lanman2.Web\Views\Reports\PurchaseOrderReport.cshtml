@model Lanman2.Default.PurchaseOrderReportData

<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0" />
	<title>Purchase Order Report</title>
	<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/css/bootstrap.min.css" />
	<style>
		body {
			font-size: 14px;
		}

		h1 {
			color: red;
			font-size: xx-large;
			vertical-align: central;
		}

		.container {
			width: 100%;
			max-width: 600px;
		}

		table {
			margin-top: 20px;
		}

		th, td {
			text-align: left;
		}

		h5 {
			margin-bottom: 5px;
		}

		h2 {
			font-size: xx-large;
		}

		hr {
			display: block;
			margin-top: 0.5em;
			margin-bottom: 0.5em;
			margin-left: auto;
			margin-right: auto;
			border-style: inset;
			border-width: 3px;
		}

		.row {
			/* display: -webkit-box; */
		}

		#total {
			text-align: right;
		}

		.invoice-header {
			height: 110px; /* Adjust the height as needed */
			/*background-color: #808080;
							padding: 10px;*/
		}
	</style>
</head>

<body>
	<section class="invoice">
		<header class="invoice-header">
			<div>
				<div style="width: 100%; margin: 0 auto; text-align: left;">
					<h1>
						<img src="~/Content/site/images/Mi_Motion_Image.png" alt="Logo" width="300" height="30" />
						@* <span style="vertical-align: middle">MOTION INDUSTRIES</span>  *@
					</h1>
				</div>
				<div style="width: 100%; margin: 0 auto; text-align: right">
					<h2>
						<span>Purchase Order</span>
					</h2>
				</div>
			</div>
			<hr>
		</header>

		<div style="text-align: left; vertical-align: middle">
			<b>PO#: @Model.PurchaseOrder.PurchaseOrderNo</b>
			<span style="margin-left: 65%;"></span>
			<b>Date:</b> @Model.PurchaseOrder.PurchaseOrderCreatedDate?.ToString("MM/dd/yyyy")
		</div>
		<div style="text-align: left; vertical-align: middle">
			Credit Terms: Lease
		</div>

		@*  <div class="row invoice-info">
            <div class="col-xs-4 invoice-col" style="text-align: right">
                <p>Date: @Model.PurchaseOrder.PurchaseOrderCreatedDate?.ToString("MM/dd/yyyy")</p>
            </div>
            <div class="col-xs-4 invoice-col">
            </div>
            <div class="col-xs-4 invoice-col">
                <b>PO # @Model.PurchaseOrder.PurchaseOrderNo</b><br>
            </div>
        </div> *@

		<table style="width: 100%; border-collapse: collapse; border: none; border-style: hidden;">
			<caption>PurchaseOrder Report page table</caption>
			<thead>
				<tr>
					<th scope="col"></th>
					<th scope="col"></th>
				</tr>
			</thead>
			<tbody>
				<tr>
					<td style="width: 33.33%; padding-right: 20px; vertical-align: top; border: none;">
						<strong>Supplier Address:</strong>
						<address>
							@Model.Vendor.VendorName <br>
							@Model.Vendor.Address1<br>
							@Model.Vendor.CityName, @Model.Vendor.StateName @Model.Vendor.Zip<br>
						</address>
					</td>
					<td style="width: 33.33%; padding-right: 20px; vertical-align: top; border: none;">
						<strong>Ship To:</strong>
						<address>
							Motion Industries, Inc.<br>
							@Model.PurchaseOrder.ShippingAddressesAddress<br>
							@Model.PurchaseOrder.ShippingAddressesCityName, @Model.PurchaseOrder.ShippingAddressesStateName @Model.PurchaseOrder.ShippingAddressesZIPCode<br>
							Attn: @Model.PurchaseOrder.ShippingAttention<br>
						</address>
					</td>
					<td style="width: 33.33%; vertical-align: top; border: none;">
						<strong>Bill To:</strong>
						<address>
							Motion Industries, Inc.<br>
							1605 Alton Road<br>
							Birmingham, AL 35210<br>
							Attn:  IT Expenses<br>
							<a href="mailto:<EMAIL>"><EMAIL></a>
						</address>
					</td>
				</tr>
			</tbody>
		</table>

		<div class="row">
			<b>Special Notes:</b><br>
			@Model.PurchaseOrder.PurchaseOrderNotes<br>
			@* <b>Payment Due:</b> @Model.Order.OrderDate.Value.ToShortDateString()<br> *@
		</div>

		<div class="row">
			<div class="col-xs-12 table-responsive">
				<table class="table table-boxed">
					<caption>PurchaseOrder table inside the table-boxed for special notes</caption>
					<thead style="border: 1px solid black;">
						<tr style="border: 1px solid black;">
							<th scope="col" style="border: 1px solid black; width:5%; text-align:center">Qty</th>
							<th scope="col" style="border: 1px solid black; width:20%; text-align:center;">Part #</th>
							<th scope="col" style="border: 1px solid black; width:50%; text-align:center;">Description</th>
							<th scope="col" style="border: 1px solid black; width:12%; text-align:center;">Unit Amt.</th>
							<th scope="col" style="border: 1px solid black; width:13%; text-align:center;">Ext. Amt.</th>
						</tr>
					</thead>
					<tbody>
						@foreach (var d in Model.Details)
						{
							<tr>
								<td style="text-align:center">@(d.Quantity?.ToString("0") ?? "")</td>
								<td>@d.ItemName</td>
								<td>@d.ItemDescription</td>
								<td style="text-align:right">$ @(d.UnitPrice?.ToString("#,##0.00") ?? "")</td>
								<td style="text-align:right">$ @(((d.Quantity ?? 0) * (d.UnitPrice ?? 0)).ToString("#,##0.00") ?? "")</td>
							</tr>
						}
					</tbody>
					<tfoot>
						<tr>
							<th id="total" colspan="4">Total (without tax)</th>
							<td style="text-align:right">$ @((Model.Details.Sum(a => ((a.Quantity ?? 0) * (a.UnitPrice ?? 0)))).ToString("#,##0.00") ?? "")</td>
						</tr>
					</tfoot>
				</table>
			</div>
		</div>
	</section>


</body>
</html>
