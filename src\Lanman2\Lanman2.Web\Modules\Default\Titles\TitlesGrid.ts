﻿import { TitlesColumns, TitlesRow, TitlesService } from '@/ServerTypes/Default';
import { Decorators, EntityGrid } from '@serenity-is/corelib';
import { TitlesDialog } from './TitlesDialog';

@Decorators.registerClass('Lanman2.Default.TitlesGrid')
export class TitlesGrid extends EntityGrid<TitlesRow, any> {
    protected getColumnsKey() { return TitlesColumns.columnsKey; }
    protected getDialogType() { return TitlesDialog; }
    protected getRowDefinition() { return TitlesRow; }
    protected getService() { return TitlesService.baseUrl; }

}