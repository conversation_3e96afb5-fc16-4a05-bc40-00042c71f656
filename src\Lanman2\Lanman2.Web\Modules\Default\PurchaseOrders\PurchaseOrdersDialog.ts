import { CostCentersRow, EmployeesRow, PurchaseOrderDetailsRow, PurchaseOrderDetailsService, PurchaseOrdersForm, PurchaseOrdersRow, PurchaseOrdersService, ShippingAddressesRow } from '@/ServerTypes/Default';
import { Decorators, EditorUtils, EntityDialog, reloadLookup, serviceRequest, toId } from '@serenity-is/corelib';
import { ReportHelper } from "@serenity-is/extensions";

@Decorators.registerClass('Lanman2.Default.PurchaseOrdersDialog')
export class PurchaseOrdersDialog extends EntityDialog<PurchaseOrdersRow, any> {
    protected getFormKey() { return PurchaseOrdersForm.formKey; }
    protected getRowDefinition() { return PurchaseOrdersRow; }
    protected getService() { return PurchaseOrdersService.baseUrl; }

    protected form = new PurchaseOrdersForm(this.idPrefix);
    oldPurchaseOrderId: number;

    constructor(oldPurchaseOrderIdParam: number) {
        super();
        this.oldPurchaseOrderId = oldPurchaseOrderIdParam;

        this.form.ShippingAddressesId.change(e => {
            const shippingAddressId = toId(this.form.ShippingAddressesId.value);
            if (shippingAddressId != null) {
                ShippingAddressesRow.getLookupAsync().then(lookup => {
                    const item = lookup.itemById[shippingAddressId];
                    this.form.ShippingAddressesAddress.value = item.Address;
                    this.form.ShippingAddressesCityName.value = item.CityName;
                    this.form.ShippingAddressesStateName.value = item.StateName;
                    this.form.ShippingAddressesZIPCode.value = item.ZipCode;
                });
            }
        });

        this.form.CostCenterId.changeSelect2(e => {
            if (!this.entity.PurchaseOrderNo) {
                const costCenterId = toId(this.form.CostCenterId.value);
                if (costCenterId) {
                    const dateNow = new Date();
                    this.form.PurchaseOrderCreatedDate.value = dateNow.toLocaleDateString();
                    CostCentersRow.getLookupAsync().then(lookup => {
                        const costCenter = lookup.itemById[costCenterId].CostCenterName;
                        const month = (dateNow.getMonth() + 1).toString().padStart(2, '0');
                        const day = dateNow.getDate().toString().padStart(2, '0');

                        const costCenterPO = costCenter + "-" + month + day + dateNow.getFullYear().toString().substring(2);
                        PurchaseOrdersRow.getLookupAsync().then(poLookup => {
                            const purchaseOrderCount = poLookup.items.filter(x => x.PurchaseOrderNo?.startsWith(costCenterPO)).length;
                            this.form.PurchaseOrderNo.value = costCenterPO + "-" + (purchaseOrderCount + 1);
                        });
                    });
                }
            }
        });

        this.form.AssignedOwnerEmployeeId.changeSelect2(e => {
            const employeeId = toId(this.form.AssignedOwnerEmployeeId.value);
            if (employeeId) {
                EmployeesRow.getLookupAsync().then(lookup => {
                    const employee = lookup.itemById[employeeId];
                    this.form.ShippingAttention.value = employee.FullName;
                });
            }
        });
    }

    protected afterLoadEntity() {
        super.afterLoadEntity();

        this.form.IsReOrderInfoGatheringComplete.getGridField().toggle(false);
        this.form.IsReplacementPOComplete.getGridField().toggle(false);

        if (!this.entity.PurchaseOrderNo) {
            let costCenterId = toId(this.entity.CostCenterId);
            if (costCenterId) {

                let dateNow = new Date();
                this.form.PurchaseOrderCreatedDate.value = dateNow.toLocaleDateString();
                CostCentersRow.getLookupAsync().then(a => {
                    let costCenter = a.itemById[costCenterId].CostCenterName;
                    let month = (dateNow.getMonth() + 1).toString().padStart(2, '0');
                    let day = dateNow.getDate().toString().padStart(2, '0');

                    let costCenterPO = costCenter + "-" + month + day + dateNow.getFullYear().toString().substring(2);
                    PurchaseOrdersRow.getLookupAsync().then(b => {
                        let purchaseOrderCount = b.items.filter(x => x.PurchaseOrderNo?.startsWith(costCenterPO)).length;

                        this.form.PurchaseOrderNo.value = costCenterPO + "-" + (purchaseOrderCount + 1);
                    });
                });
            }
        }
    }

    protected updateInterface() {
        super.updateInterface();

        EditorUtils.setReadonly(this.form.ShippingAddressesAddress.element, true);
        EditorUtils.setReadonly(this.form.ShippingAddressesCityName.element, true);
        EditorUtils.setReadonly(this.form.ShippingAddressesStateName.element, true);
        EditorUtils.setReadonly(this.form.ShippingAddressesZIPCode.element, true);
    }

    // Refactored onSaveSuccess now async and calls smaller helpers
    protected async onSaveSuccess(response) {
        reloadLookup(PurchaseOrdersRow.lookupKey);
        reloadLookup(PurchaseOrderDetailsRow.lookupKey);
        const entityId = response.EntityId;

        if (this.oldPurchaseOrderId > 0) {
            try {
                await this.handleOldPurchaseOrderUpdate(entityId);
            }
            catch (err) {
                console.error("Error during onSaveSuccess:", err);
            }
        }
    }

    private async handleOldPurchaseOrderUpdate(entityId: number) {
        const purchaseOrderLookup = await PurchaseOrdersRow.getLookupAsync();
        const oldPurchaseOrder = purchaseOrderLookup.itemById[this.oldPurchaseOrderId];

        if (this.form.IsReOrderInfoGatheringComplete && !this.form.IsReOrderInfoGatheringComplete.element[0].hidden) {
            oldPurchaseOrder.IsReOrderInfoGatheringComplete = this.form.IsReOrderInfoGatheringComplete?.value;
        }

        if (this.form.IsReplacementPOComplete && !this.form.IsReplacementPOComplete.element[0].hidden) {
            oldPurchaseOrder.IsReplacementPOComplete = this.form.IsReplacementPOComplete?.value;
        }

        await this.updatePurchaseOrderDetails(entityId);
        await this.updateReplacementPurchaseOrder(entityId);
        await this.updateOldPurchaseOrder(oldPurchaseOrder);
    }

    private async updatePurchaseOrderDetails(entityId: number) {
        const purchaseOrderDetailsLookup = await PurchaseOrderDetailsRow.getLookupAsync();
        const details = purchaseOrderDetailsLookup.items.filter(x =>
            x.PurchaseOrderId === this.oldPurchaseOrderId &&
            (x.ItemTypeName === 'Desktop' || x.ItemTypeName === 'Laptop'));

        if (details.length > 0 && (!details[0].ReplacementPurchaseOrderId || details[0].ReplacementPurchaseOrderId < 0)) {
            details[0].ReplacementPurchaseOrderId = entityId;

            await new Promise<void>((resolve, reject) => {
                serviceRequest(PurchaseOrderDetailsService.Methods.Update, {
                    EntityId: details[0].PurchaseOrderDetailId,
                    Entity: details[0]
                }, res => resolve());
            });
        }
    }

    private async updateReplacementPurchaseOrder(entityId: number) {
        const purchaseOrderLookup = await PurchaseOrdersRow.getLookupAsync();
        const replacementPO = purchaseOrderLookup.itemById[entityId];
        replacementPO.IsReOrderInfoGatheringComplete = false;
        replacementPO.IsReplacementPOComplete = false;

        await new Promise<void>((resolve, reject) => {
            serviceRequest(PurchaseOrdersService.Methods.Update, {
                EntityId: entityId,
                Entity: replacementPO
            }, res => resolve());
        });
    }

    private async updateOldPurchaseOrder(oldPurchaseOrder: PurchaseOrdersRow) {
        await new Promise<void>((resolve, reject) => {
            serviceRequest(PurchaseOrdersService.Methods.Update, {
                EntityId: this.oldPurchaseOrderId,
                Entity: oldPurchaseOrder
            }, res => resolve());
        });
    }

    getToolbarButtons() {
        const buttons = super.getToolbarButtons();

        buttons.push(ReportHelper.createToolButton({
            title: 'Purchase Order',
            cssClass: 'export-pdf-button',
            reportKey: 'PurchaseOrderReport',
            getParams: () => ({
                OrderID: this.get_entityId()
            })
        }));

        return buttons;
    }
}
