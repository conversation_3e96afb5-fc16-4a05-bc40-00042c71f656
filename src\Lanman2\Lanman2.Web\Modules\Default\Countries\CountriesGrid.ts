﻿import { CountriesColumns, CountriesRow, CountriesService } from '@/ServerTypes/Default';
import { Decorators, EntityGrid } from '@serenity-is/corelib';
import { CountriesDialog } from './CountriesDialog';

@Decorators.registerClass('Lanman2.Default.CountriesGrid')
export class CountriesGrid extends EntityGrid<CountriesRow, any> {
    protected getColumnsKey() { return CountriesColumns.columnsKey; }
    protected getDialogType() { return CountriesDialog; }
    protected getRowDefinition() { return CountriesRow; }
    protected getService() { return CountriesService.baseUrl; }

}