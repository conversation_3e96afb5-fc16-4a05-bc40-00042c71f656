import { ItemsRow, ItemTypesRow, PurchaseOrderDetailsColumns, PurchaseOrderDetailsRow } from '@/ServerTypes/Default';
import { alertDialog, Decorators, toId, tryFirst, indexOf } from '@serenity-is/corelib';
import { GridEditorBase } from '@serenity-is/extensions';
import { PurchaseOrderDetailsEditDialog } from './PurchaseOrderDetailsEditDialog';

@Decorators.registerEditor('Lanman2.Default.PurchaseOrderDetailsEditor')
export class PurchaseOrderDetailsEditor extends GridEditorBase<PurchaseOrderDetailsRow> {

    protected getColumnsKey() { return PurchaseOrderDetailsColumns.columnsKey; }
    protected getLocalTextPrefix() { return PurchaseOrderDetailsRow.localTextPrefix; }

    protected getDialogType() { return PurchaseOrderDetailsEditDialog; }

    protected getAddButtonCaption() {
        return "Add";
    }

    protected getQuickFilters() {
        return [];
    }

    protected validateEntity(row: PurchaseOrderDetailsRow, id: number) {
        if (!super.validateEntity(row, id))
            return false;

        row.ItemId = toId(row.ItemId);

        if (!row.ItemId) {
            alertDialog('Item should be selected!');
            return false;
        }

        let sameItem = tryFirst(this.view.getItems(), x => x.ItemId === row.ItemId);
        if (sameItem && this.id(sameItem) !== id) {
            alertDialog('This item is already in order details!');
            return false;
        }

        let itemTypes = ItemTypesRow.getLookup().items;
        let desktopItemTypeId = itemTypes.find(a => a.ItemTypeName === 'Desktop').ItemTypeId;
        let laptopItemTypeId = itemTypes.find(a => a.ItemTypeName === 'Laptop').ItemTypeId;

        let laptopAndDesktopCount = this.view.getItems().filter(item => item.ItemTypeId == desktopItemTypeId || item.ItemTypeId == laptopItemTypeId).length;
        if (laptopAndDesktopCount > 0 && (!id) && (row.ItemTypeId == desktopItemTypeId || row.ItemTypeId == laptopItemTypeId)) {
            alertDialog('Only one desktop or laptop can be added to the purchase order!');
            return false;
        }
        

        id ??= row[this.getIdProperty()];

        ItemsRow.getLookupAsync().then(lookup => {
            let purchaseOrderDetailRow = this.view?.getItemById?.(id);
            let item = lookup.itemById[row.ItemId];
            if (purchaseOrderDetailRow) {
                purchaseOrderDetailRow.ItemName = item.ItemName;
                purchaseOrderDetailRow.ItemDescription = item.ItemDescription;
                
                this.view.updateItem(id, purchaseOrderDetailRow);
            }
        });

        return true;
    }

    protected getColumns() {
        let columns = super.getColumns();

        columns.splice(indexOf(columns, x => x.field == "PurchaseOrderNo"), 1);
        columns.splice(indexOf(columns, x => x.field == "ReplaceEmailSentOnDate"), 1);
        columns.splice(indexOf(columns, x => x.field == "ReplaceEmailResponseReceivedDate"), 1);
        columns.splice(indexOf(columns, x => x.field == "ReplacementType"), 1);
        columns.splice(indexOf(columns, x => x.field == "IsDiscoveryComplete"), 1);
        columns.splice(indexOf(columns, x => x.field == "ReplacementPurchaseOrderPurchaseOrderNo"), 1);
        columns.splice(indexOf(columns, x => x.field == "ReplacementNotes"), 1);
        columns.splice(indexOf(columns, x => x.field == "IsReOrderInfoGatheringComplete"), 1);
        columns.splice(indexOf(columns, x => x.field == "IsReplacementPOComplete"), 1);
        columns.splice(indexOf(columns, x => x.field == "ReturnDate"), 1);
        columns.splice(indexOf(columns, x => x.field == "ReturnEmailDate"), 1);
        columns.splice(indexOf(columns, x => x.field == "ReturnTrackingNumber"), 1);
        columns.splice(indexOf(columns, x => x.field == "ItemDisabledDate"), 1);
        columns.splice(indexOf(columns, x => x.field == "IsReturnComplete"), 1);
        columns.splice(indexOf(columns, x => x.field == "PurchaseOrderCreatedDate"), 1);
        columns.splice(indexOf(columns, x => x.field == "LeaseExpiryDate"), 1);
        

        return columns;
    }
}