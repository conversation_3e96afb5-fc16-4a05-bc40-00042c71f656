﻿import { ItemTypesColumns, ItemTypesRow, ItemTypesService } from '@/ServerTypes/Default';
import { Decorators, EntityGrid } from '@serenity-is/corelib';
import { ItemTypesDialog } from './ItemTypesDialog';

@Decorators.registerClass('Lanman2.Default.ItemTypesGrid')
export class ItemTypesGrid extends EntityGrid<ItemTypesRow, any> {
    protected getColumnsKey() { return ItemTypesColumns.columnsKey; }
    protected getDialogType() { return ItemTypesDialog; }
    protected getRowDefinition() { return ItemTypesRow; }
    protected getService() { return ItemTypesService.baseUrl; }

}