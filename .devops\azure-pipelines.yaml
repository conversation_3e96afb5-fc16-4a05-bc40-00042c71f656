trigger:
  batch: true
  tags:
    include:
      - "*"
  branches:
    include:
      - "main"

pr:
  autoCancel: true
  branches:
    include:
      - "*"

variables:
  - template: variables-common.yaml
  - template: variables.yaml
  - group: cpe-mi-lease-manager-env

resources:
  repositories:
    - repository: templates
      type: github
      name: motion-it/azure-pipeline-templates
      endpoint: motion-it
      ref: refs/tags/v1.3.2

    - repository: cdcentralrepo
      type: github
      name: motion-it/cpe-argo-central-apps
      endpoint: motion-it
      ref: main

stages:
  - template: /IAC/gke-with-helm-deploy/argocd-ado-master.yaml@templates
    parameters:
      RELEASE_MANAGERS: ${{ variables.RELEASE_MANAGERS }}
      BRANCH_NAME: ${{ variables.BRANCH_NAME }}
      pool_mi_dev_cloud: ${{ variables.pool_mi_dev_cloud }}
      appname: ${{ variables.appname }}
      registry: ${{ variables.registry }}
      imagename: ${{ variables.imagename }}
      imagetag: ${{ variables.imagetag }}
      servicename: ${{ variables.servicename }}
      reponame: ${{ variables.reponame }}
      preConfigureDocker: ${{ variables.preConfigureDocker }}
      #Sonar
      dockerfileSonar: ${{ variables.dockerfileSonar }}
      sonartemplatename: ${{ variables.sonartemplatename }}
      #Unit Testing
      dockerfileUnitTesting: ${{ variables.dockerfileUnitTesting }}
      #BuildPublishImages
      containerPort: ${{ variables.containerPort }}
      dockerfileJvm: ${{ variables.dockerfileJvm }}
      # HelmPackaging
      helmregistry: ${{ variables.helmregistry }}
      helmpath: ${{ variables.helmpath }}
      chartversion: ${{ variables.chartversion }}
      appversion: ${{ variables.appversion }}
      #Feature Deploy
      pool_ft: ${{ variables.pool_ft }}
      tfvarsfile_ft: ${{ variables.tfvarsfile_ft }}
      backendconfig_ft: ${{ variables.backendconfig_ft }}
      hostname_ft: ${{ variables.hostname_ft }}
      cluster_ft: ${{ variables.cluster_ft }}
      project_id_ft: ${{ variables.project_id_ft }}
      region_ft: ${{ variables.region_ft }}
      environment_ft: ${{ variables.environment_ft }}
      pathDevops: ${{ variables.pathDevops }}
      namespace_ft: ${{ variables.namespace_ft }}
      sa_ft: ${{ variables.sa_ft }}
      #DevDeploy
      pool_dv: ${{ variables.pool_dv }}
      tfvarsfile_dv: ${{ variables.tfvarsfile_dv }}
      backendconfig_dv: ${{ variables.backendconfig_dv }}
      hostname_dv: ${{ variables.hostname_dv }}
      cluster_dv: ${{ variables.cluster_dv }}
      project_id_dv: ${{ variables.project_id_dv }}
      region_dv: ${{ variables.region_dv }}
      environment_dv: ${{ variables.environment_dv }}
      namespace_dv: ${{ variables.namespace_dv }}
      sa_dv: ${{ variables.sa_dv }}
      #QaDeploy
      pool_qa: ${{ variables.pool_qa }}
      tfvarsfile_qa: ${{ variables.tfvarsfile_qa }}
      backendconfig_qa: ${{ variables.backendconfig_qa }}
      hostname_qa: ${{ variables.hostname_qa }}
      cluster_qa_use4: ${{ variables.cluster_qa_use4 }}
      cluster_qa_usc1: ${{ variables.cluster_qa_usc1 }}
      region_qa_use4: ${{ variables.region_qa_use4 }}
      region_qa_usc1: ${{ variables.region_qa_usc1 }}
      project_id_qa: ${{ variables.project_id_qa }}
      environment_qa: ${{ variables.environment_qa }}
      namespace_qa: ${{ variables.namespace_qa }}
      sa_qa: ${{ variables.sa_qa }}
      gate_qa: ${{ variables.gate_qa }}
      #Performance Testing
      launchName: ${{ variables.launchName }}
      nlpPath: ${{ variables.nlpPath }}
      #PdDeploy
      pool_pd: ${{ variables.pool_pd }}
      tfvarsfile_pd: ${{ variables.tfvarsfile_pd }}
      backendconfig_pd: ${{ variables.backendconfig_pd }}
      hostname_pd: ${{ variables.hostname_pd }}
      cluster_pd_use4: ${{ variables.cluster_pd_use4 }}
      cluster_pd_usc1: ${{ variables.cluster_pd_usc1 }}
      region_pd_use4: ${{ variables.region_pd_use4 }}
      region_pd_usc1: ${{ variables.region_pd_usc1 }}
      project_id_pd: ${{ variables.project_id_pd }}
      environment_pd: ${{ variables.environment_pd }}
      namespace_pd: ${{ variables.namespace_pd }}
      sa_pd: ${{ variables.sa_pd }}
      gate_pd: ${{ variables.gate_pd }}
