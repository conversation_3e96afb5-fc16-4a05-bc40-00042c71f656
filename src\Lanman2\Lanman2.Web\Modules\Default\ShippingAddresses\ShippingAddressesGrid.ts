﻿import { ShippingAddressesColumns, ShippingAddressesRow, ShippingAddressesService } from '@/ServerTypes/Default';
import { Decorators, EntityGrid } from '@serenity-is/corelib';
import { ShippingAddressesDialog } from './ShippingAddressesDialog';

@Decorators.registerClass('Lanman2.Default.ShippingAddressesGrid')
export class ShippingAddressesGrid extends EntityGrid<ShippingAddressesRow, any> {
    protected getColumnsKey() { return ShippingAddressesColumns.columnsKey; }
    protected getDialogType() { return ShippingAddressesDialog; }
    protected getRowDefinition() { return ShippingAddressesRow; }
    protected getService() { return ShippingAddressesService.baseUrl; }

}