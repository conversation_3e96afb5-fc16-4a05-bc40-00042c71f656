﻿import { ReplacementTypesColumns, ReplacementTypesRow, ReplacementTypesService } from '@/ServerTypes/Default';
import { Decorators, EntityGrid } from '@serenity-is/corelib';
import { ReplacementTypesDialog } from './ReplacementTypesDialog';

@Decorators.registerClass('Lanman2.Default.ReplacementTypesGrid')
export class ReplacementTypesGrid extends EntityGrid<ReplacementTypesRow, any> {
    protected getColumnsKey() { return ReplacementTypesColumns.columnsKey; }
    protected getDialogType() { return ReplacementTypesDialog; }
    protected getRowDefinition() { return ReplacementTypesRow; }
    protected getService() { return ReplacementTypesService.baseUrl; }

}