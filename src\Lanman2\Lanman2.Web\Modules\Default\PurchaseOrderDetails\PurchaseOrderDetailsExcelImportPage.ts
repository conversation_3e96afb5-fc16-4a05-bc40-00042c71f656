import { Decorators, EntityGrid, PropertyDialog, ToolButton,  notifyError, notifyInfo, initFullHeightGridPage } from "@serenity-is/corelib";
import { PurchaseOrderDetailExcelImportForm, PurchaseOrderDetailsColumns, PurchaseOrderDetailsRow, PurchaseOrderDetailsService } from "../../ServerTypes/Default";
import { PurchaseOrderDetailsDialog } from "./PurchaseOrderDetailsDialog";

/*export default () => gridPageInit(PurchaseOrderDetailExcelImportGrid)*/

export default function pageInit() {
    initFullHeightGridPage(new PurchaseOrderDetailExcelImportGrid($('#GridDiv')).element);
}

@Decorators.registerClass('Lanman2.Default.PurchaseOrderDetailExcelImportGrid')
export class PurchaseOrderDetailExcelImportGrid<P = {}> extends EntityGrid<PurchaseOrderDetailsRow, P> {

    protected getColumnsKey() { return PurchaseOrderDetailsColumns.columnsKey; }
    protected getDialogType() { return PurchaseOrderDetailsDialog; }
    protected getRowDefinition() { return PurchaseOrderDetailsRow; }
    protected getService() { return PurchaseOrderDetailsService.baseUrl; }

    /**
     * This method is called to get list of buttons to be created.
     */
    protected getButtons(): ToolButton[] {

        // call base method to get list of buttons
        let buttons = super.getButtons();

        // add our import button
        buttons.push({
            title: 'Import From Excel',
            cssClass: 'export-xlsx-button',
            separator: true,
            onClick: () => {
                // open import dialog, let it handle rest
                let dialog = new PurchaseOrderDetailExcelImportDialog();
                dialog.element.on('dialogclose', () => {
                    this.refresh();
                    dialog = null;
                });
                dialog.dialogOpen();
            }
        });

        return buttons;
    }
}


@Decorators.registerClass('Lanman2.Default.PurchaseOrderDetailExcelImportDialog')
export class PurchaseOrderDetailExcelImportDialog extends PropertyDialog<any, any> {

    private form: PurchaseOrderDetailExcelImportForm;

    protected getFormKey() { return PurchaseOrderDetailExcelImportForm.formKey; }

    constructor() {
        super();

        this.form = new PurchaseOrderDetailExcelImportForm(this.idPrefix);
    }

    protected getDialogTitle(): string {
        return "Excel Import";
    }

    protected getDialogButtons() {
        return [
            {
                text: 'Import',
                click: () => {
                    if (!this.validateBeforeSave())
                        return;

                    if (!this.form.FileName.value?.Filename) {
                        notifyError("Please select a file!");
                        return;
                    }

                    PurchaseOrderDetailsService.ExcelImport({
                        FileName: this.form.FileName.value.Filename
                    }, response => {
                        notifyInfo(
                            'Inserted: ' + (response.Inserted || 0) +
                            ', Updated: ' + (response.Updated || 0));

                        if (response.ErrorList != null && response.ErrorList.length > 0) {
                            notifyError(response.ErrorList.join(',\r\n '));
                        }

                        this.dialogClose();
                    });
                },
            },
            {
                text: 'Cancel',
                click: () => this.dialogClose()
            }
        ];
    }
}

