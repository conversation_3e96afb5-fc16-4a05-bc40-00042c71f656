﻿import { ItemMakeColumns, ItemMakeRow, ItemMakeService } from '@/ServerTypes/Default';
import { Decorators, EntityGrid } from '@serenity-is/corelib';
import { ItemMakeDialog } from './ItemMakeDialog';

@Decorators.registerClass('Lanman2.Default.ItemMakeGrid')
export class ItemMakeGrid extends EntityGrid<ItemMakeRow, any> {
    protected getColumnsKey() { return ItemMakeColumns.columnsKey; }
    protected getDialogType() { return ItemMakeDialog; }
    protected getRowDefinition() { return ItemMakeRow; }
    protected getService() { return ItemMakeService.baseUrl; }

}