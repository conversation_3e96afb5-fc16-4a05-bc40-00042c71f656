﻿import { DesignationsColumns, DesignationsRow, DesignationsService } from '@/ServerTypes/Default';
import { Decorators, EntityGrid } from '@serenity-is/corelib';
import { DesignationsDialog } from './DesignationsDialog';

@Decorators.registerClass('Lanman2.Default.DesignationsGrid')
export class DesignationsGrid extends EntityGrid<DesignationsRow, any> {
    protected getColumnsKey() { return DesignationsColumns.columnsKey; }
    protected getDialogType() { return DesignationsDialog; }
    protected getRowDefinition() { return DesignationsRow; }
    protected getService() { return DesignationsService.baseUrl; }

}