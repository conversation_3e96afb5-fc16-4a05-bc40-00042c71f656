{"version": 3, "sources": ["../../../../../../Modules/Membership/Account/Login/LoginPage.tsx"], "sourcesContent": ["/** @jsxImportSource jsx-dom */\r\nimport { LoginForm, LoginRequest } from \"@/ServerTypes/Membership\";\r\nimport { Texts } from \"@/ServerTypes/Texts\";\r\nimport { PropertyPanel } from \"@serenity-is/corelib\";\r\nimport { ErrorHandling, format, getCookie, notifyError, parseQueryString, resolveUrl, serviceCall, tryGetText } from \"@serenity-is/corelib\";\r\nimport { AccountPanelTitle } from \"../AccountPanelTitle\";\r\n\r\nexport default function pageInit(opt?: { activated: string }) {\r\n    var loginPanel = new LoginPanel($('#LoginPanel'));\r\n\r\n    if (opt?.activated) {\r\n        loginPanel.form.Username.value = opt.activated;\r\n        loginPanel.form.Password.element.focus();\r\n    }\r\n}\r\n\r\nclass LoginPanel extends PropertyPanel<LoginRequest, any> {\r\n\r\n    public readonly form = new LoginForm(this.idPrefix);\r\n\r\n    protected getFormKey() { return LoginForm.formKey; }\r\n\r\n    constructor(element: JQuery, options?: any) {\r\n        super(element, options);\r\n    }\r\n\r\n    protected loginClick() {\r\n        if (!this.validateForm())\r\n            return;\r\n\r\n        var request = this.getSaveEntity();\r\n\r\n        serviceCall({\r\n            url: resolveUrl('~/Account/Login'),\r\n            request: request,\r\n            onSuccess: () => {\r\n                this.redirectToReturnUrl();\r\n            },\r\n            onError: response => {\r\n\r\n                if (response?.Error?.Code === \"RedirectUserTo\") {\r\n                    window.location.href = response.Error.Arguments;\r\n                    return;\r\n                }\r\n\r\n                if (response?.Error?.Message?.length) {\r\n                    notifyError(response.Error.Message);\r\n                    this.form.Password.element.focus();\r\n\r\n                    return;\r\n                }\r\n\r\n                ErrorHandling.showServiceError(response.Error);\r\n            }\r\n        });\r\n\r\n    }\r\n\r\n    protected getReturnUrl() {\r\n        var q = parseQueryString();\r\n        return q['returnUrl'] || q['ReturnUrl'];\r\n    }\r\n\r\n    protected redirectToReturnUrl() {\r\n        var returnUrl = this.getReturnUrl();\r\n        if (returnUrl && /^\\//.test(returnUrl)) {\r\n            var hash = window.location.hash;\r\n            if (hash != null && hash != '#')\r\n                returnUrl += hash;\r\n            window.location.href = returnUrl;\r\n        }\r\n        else {\r\n            window.location.href = resolveUrl('~/');\r\n        }\r\n    }\r\n\r\n    protected renderContents() {\r\n        const id = this.useIdPrefix();\r\n        const myTexts = Texts.Forms.Membership.Login;\r\n        this.element.empty().append(<>\r\n            <AccountPanelTitle />\r\n            <div class=\"s-Panel p-4\">\r\n                <h5 class=\"text-center my-4\">{myTexts.LoginToYourAccount}</h5>\r\n                <form id={id.Form} action=\"\">\r\n                    <div id={id.PropertyGrid}></div>\r\n                    <div class=\"px-field\">\r\n                        <a class=\"float-end text-decoration-none\" href={resolveUrl('~/Account/ForgotPassword')}>\r\n                            {myTexts.ForgotPassword}\r\n                        </a>\r\n                    </div>\r\n                    <div class=\"px-field\">\r\n                        <button id={id.LoginButton} type=\"submit\" class=\"btn btn-primary my-3 w-100\"\r\n                            onClick={e => {\r\n                                e.preventDefault();\r\n                                this.loginClick();\r\n                            }}>\r\n                            {myTexts.SignInButton}\r\n                        </button>\r\n                    </div>\r\n                </form>\r\n            </div>\r\n        </>);\r\n    }\r\n}"], "mappings": "iNAGA,IAAAA,EAA8B,SAC9BA,EAAqH,SAGtG,SAARC,EAA0BC,EAA6B,CAC1D,IAAIC,EAAa,IAAIC,EAAW,EAAE,aAAa,CAAC,EAE5CF,GAAA,MAAAA,EAAK,YACLC,EAAW,KAAK,SAAS,MAAQD,EAAI,UACrCC,EAAW,KAAK,SAAS,QAAQ,MAAM,EAE/C,CAPwBE,EAAAJ,EAAA,YASxB,IAAMK,EAAN,MAAMA,UAAmB,eAAiC,CAMtD,YAAYC,EAAiBC,EAAe,CACxC,MAAMD,EAASC,CAAO,EAL1B,KAAgB,KAAO,IAAIC,EAAU,KAAK,QAAQ,CAMlD,CAJU,YAAa,CAAE,OAAOA,EAAU,OAAS,CAMzC,YAAa,CACnB,GAAK,KAAK,aAAa,EAGvB,KAAIC,EAAU,KAAK,cAAc,KAEjC,eAAY,CACR,OAAK,cAAW,iBAAiB,EACjC,QAASA,EACT,UAAW,IAAM,CACb,KAAK,oBAAoB,CAC7B,EACA,QAASC,GAAY,CAtCjC,IAAAC,EAAAC,EAAAC,EAwCgB,KAAIF,EAAAD,GAAA,YAAAA,EAAU,QAAV,YAAAC,EAAiB,QAAS,iBAAkB,CAC5C,OAAO,SAAS,KAAOD,EAAS,MAAM,UACtC,MACJ,CAEA,IAAIG,GAAAD,EAAAF,GAAA,YAAAA,EAAU,QAAV,YAAAE,EAAiB,UAAjB,MAAAC,EAA0B,OAAQ,IAClC,eAAYH,EAAS,MAAM,OAAO,EAClC,KAAK,KAAK,SAAS,QAAQ,MAAM,EAEjC,MACJ,CAEA,gBAAc,iBAAiBA,EAAS,KAAK,CACjD,CACJ,CAAC,EAEL,CAEU,cAAe,CACrB,IAAII,KAAI,oBAAiB,EACzB,OAAOA,EAAE,WAAgBA,EAAE,SAC/B,CAEU,qBAAsB,CAC5B,IAAIC,EAAY,KAAK,aAAa,EAClC,GAAIA,GAAa,MAAM,KAAKA,CAAS,EAAG,CACpC,IAAIC,EAAO,OAAO,SAAS,KACvBA,GAAQ,MAAQA,GAAQ,MACxBD,GAAaC,GACjB,OAAO,SAAS,KAAOD,CAC3B,MAEI,OAAO,SAAS,QAAO,cAAW,IAAI,CAE9C,CAEU,gBAAiB,CACvB,IAAME,EAAK,KAAK,YAAY,EACtBC,EAAUC,EAAM,MAAM,WAAW,MACvC,KAAK,QAAQ,MAAM,EAAE,OAAOC,EAAAC,EAAA,CACxB,UAAAD,EAACE,EAAA,EAAkB,EACnBF,EAAC,OAAI,MAAM,cACP,UAAAA,EAAC,MAAG,MAAM,mBAAoB,SAAAF,EAAQ,mBAAmB,EACzDE,EAAC,QAAK,GAAIH,EAAG,KAAM,OAAO,GACtB,UAAAG,EAAC,OAAI,GAAIH,EAAG,aAAc,EAC1BG,EAAC,OAAI,MAAM,WACP,SAAAA,EAAC,KAAE,MAAM,iCAAiC,QAAM,cAAW,0BAA0B,EAChF,SAAAF,EAAQ,eACb,EACJ,EACAE,EAAC,OAAI,MAAM,WACP,SAAAA,EAAC,UAAO,GAAIH,EAAG,YAAa,KAAK,SAAS,MAAM,6BAC5C,QAASM,GAAK,CACVA,EAAE,eAAe,EACjB,KAAK,WAAW,CACpB,EACC,SAAAL,EAAQ,aACb,EACJ,GACJ,GACJ,GACJ,CAAG,CACP,CACJ,EAvF0Dd,EAAAC,EAAA,cAA1D,IAAMF,EAANE", "names": ["import_corelib", "pageInit", "opt", "loginPanel", "LoginPanel", "__name", "_LoginPanel", "element", "options", "LoginForm", "request", "response", "_a", "_b", "_c", "q", "returnUrl", "hash", "id", "myTexts", "Texts", "jsx", "Fragment", "AccountPanelTitle", "e"]}