﻿import { VendorsColumns, VendorsRow, VendorsService } from '@/ServerTypes/Default';
import { Decorators, EntityGrid } from '@serenity-is/corelib';
import { VendorsDialog } from './VendorsDialog';

@Decorators.registerClass('Lanman2.Default.VendorsGrid')
export class VendorsGrid extends EntityGrid<VendorsRow, any> {
    protected getColumnsKey() { return VendorsColumns.columnsKey; }
    protected getDialogType() { return VendorsDialog; }
    protected getRowDefinition() { return VendorsRow; }
    protected getService() { return VendorsService.baseUrl; }

}