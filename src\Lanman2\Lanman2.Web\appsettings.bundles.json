{
  "CssBundling": {
    "Bundles": {
      "Base": [
        "~/Serenity.Assets/Content/font-open-sans.css",
        "~/Serenity.Assets/Content/font-awesome.css",
        "~/Serenity.Assets/fonts/open-sans/open-sans.css",
        "~/Serenity.Assets/bootstrap/css/bootstrap.css",
        "~/Serenity.Assets/Content/css/select2.css",
        "~/Serenity.Assets/Content/toastr.css",
        "~/Serenity.Assets/Content/slick.grid.css",
        "~/Content/serenity/",
        "~/Serenity.Assets/nprogress/nprogress.css",
        "~/Serenity.Extensions/index.css"
      ],
      "Site": [
        "~/Serenity.Assets/Content/jquery.fileupload.css",
        "~/Serenity.Assets/Content/colorbox/jquery.colorbox.css",
        //"~/Serenity.Extensions/common-theme.css",
        "~/Content/site/site.css"
      ],
      "Pages/Dashboard": [
        "~/Scripts/jvectormap/jquery-jvectormap-1.2.2.css",
        "~/Scripts/datepicker/datepicker3.css",
        "~/Scripts/daterangepicker/daterangepicker-bs3.css"
      ]
    }
  },
  "ScriptBundling": {
    "Bundles": {
      "Base": [
        "~/Serenity.Assets/nprogress/nprogress.js",
        "~/Serenity.Assets/jquery/jquery.js",
        "~/Serenity.Assets/Scripts/jquery-ui.js",
        "~/Serenity.Assets/Scripts/jquery-ui-i18n.js",
        "~/Serenity.Assets/bootstrap/js/bootstrap.bundle.js",
        "~/Serenity.Assets/Scripts/jquery.autoNumeric.js",
        "~/Serenity.Assets/Scripts/jquery.colorbox.js",
        "~/Serenity.Assets/Scripts/jquery.dialogextendQ.js",
        "~/Serenity.Assets/Scripts/jquery.event.drag.js",
        "~/Serenity.Assets/Scripts/jquery.fileupload.js",
        "~/Serenity.Assets/Scripts/jquery.maskedinput.js",
        "~/Serenity.Assets/Scripts/jquery.validate.js",
        "~/Serenity.Assets/Scripts/select2.js",
        "~/Serenity.Assets/Scripts/SlickGrid/slick.core.js",
        "~/Serenity.Assets/Scripts/SlickGrid/slick.grid.js",
        "~/Serenity.Assets/Scripts/SlickGrid/slick.groupitemmetadataprovider.js",
        "~/Serenity.Assets/Scripts/SlickGrid/Plugins/slick.autotooltips.js",
        "~/Serenity.Assets/Scripts/SlickGrid/Plugins/slick.headerbuttons.js",
        "~/Serenity.Assets/Scripts/SlickGrid/Plugins/slick.rowselectionmodel.js",
        "~/Serenity.Assets/Scripts/SlickGrid/Plugins/slick.rowmovemanager.js",
        "~/Serenity.Assets/Scripts/sortable.js",
        "~/Serenity.Assets/Scripts/toastr.js",
        "~/Serenity.Assets/preact/preact.umd.js",
        "~/Serenity.Assets/mousetrap/mousetrap.js",
        "~/Serenity.Extensions/common-theme.js",
        "~/Serenity.Scripts/Serenity.CoreLib.js",
        "~/Serenity.Extensions/index.js"
      ],
      "Site": [
        "dynamic://ColumnsBundle",
        "dynamic://FormBundle",
        "dynamic://TemplateBundle"
      ],
      "CKEditor": [
        "~/Serenity.Assets/Scripts/ckeditor/ckeditor.js",
        "~/Serenity.Assets/Scripts/ckeditor/lang/en.js",
        "~/Serenity.Assets/Scripts/ckeditor/styles.js"
      ],
      "Pages/Dashboard": [
        "~/lib/chartjs/chart.js",
        "~/Scripts/sparkline/jquery.sparkline.min.js",
        "~/Scripts/jvectormap/jquery-jvectormap-1.2.2.min.js",
        "~/Scripts/jvectormap/jquery-jvectormap-world-mill-en.js",
        "~/Scripts/knob/jquery.knob.js",
        "~/lib/moment.js/moment.min.js",
        "~/Scripts/daterangepicker/daterangepicker.js",
        "~/Scripts/datepicker/bootstrap-datepicker.js"
      ]
    }
  }
}