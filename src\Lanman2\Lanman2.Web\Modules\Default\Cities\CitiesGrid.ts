﻿import { CitiesColumns, CitiesRow, CitiesService } from '@/ServerTypes/Default';
import { Decorators, EntityGrid } from '@serenity-is/corelib';
import { CitiesDialog } from './CitiesDialog';

@Decorators.registerClass('Lanman2.Default.CitiesGrid')
export class CitiesGrid extends EntityGrid<CitiesRow, any> {
    protected getColumnsKey() { return CitiesColumns.columnsKey; }
    protected getDialogType() { return CitiesDialog; }
    protected getRowDefinition() { return CitiesRow; }
    protected getService() { return CitiesService.baseUrl; }

}