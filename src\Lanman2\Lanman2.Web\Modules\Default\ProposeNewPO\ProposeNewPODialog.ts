import { PurchaseOrdersRow } from '@/ServerTypes/Default';
import { Decorators, indexOf } from '@serenity-is/corelib';
import { PurchaseOrdersDialog } from '../PurchaseOrders/PurchaseOrdersDialog';

@Decorators.registerClass('Lanman2.Default.ProposeNewPODialog')
export class ProposeNewPODialog extends PurchaseOrdersDialog {

    afterLoadEntity() {

        this.hideUnhideElements();

        this.setIsReOrderInfoGatheringCompleteValue();

        this.set_dialogTitle("Propose New PO");
    }

    getToolbarButtons() {
        let buttons = super.getToolbarButtons();

        buttons.splice(indexOf(buttons, x => x.title == "Undelete"), 1);
        buttons.splice(indexOf(buttons, x => x.title == "Localization"), 1);
        buttons.splice(indexOf(buttons, x => x.title == "Clone"), 1);
        buttons.splice(indexOf(buttons, x => x.title == "Delete"), 1);
        buttons.splice(indexOf(buttons, x => x.title == "Purchase Order"), 1);

        return buttons;
    }

    private hideUnhideElements() {
        this.form.PurchaseOrderNo.getGridField().toggle(false);
        this.form.PurchaseOrderNotes.getGridField().toggle(false);
        this.form.PurchaseOrderCreatedDate.getGridField().toggle(false);
        this.form.PurchaseOrderEmailSentOnDate.getGridField().toggle(false);
        this.form.IsReOrderInfoGatheringComplete.getGridField().toggle(true);
        this.form.IsReplacementPOComplete.getGridField().toggle(false);
    }

    setIsReOrderInfoGatheringCompleteValue() {
        if (this.oldPurchaseOrderId) {

            (PurchaseOrdersRow.getLookupAsync()).then(a => {
                let oldPurchaseOrder = a.itemById[this.oldPurchaseOrderId];
                this.form.IsReOrderInfoGatheringComplete.value = oldPurchaseOrder.IsReOrderInfoGatheringComplete;
            });
        }
    }

    
}