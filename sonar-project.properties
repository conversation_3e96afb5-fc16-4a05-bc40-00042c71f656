# Analysis Settings
sonar.sourceEncoding=UTF-8
sonar.sources=src/Lanman2/Lanman2.Web/
sonar.tests=src/Lanman2/Lanman2.Tests/
sonar.exclusions=**/bin/**,**/obj/**,**/*.Test.cs,**/*.Tests.cs,**/Migrations/**,src/Lanman2/Lanman2.Web/Modules/ServerTypes/**,src/Lanman2/Lanman2.Web/wwwroot/Scripts/sparkline/jquery.sparkline.js,src/Lanman2/Lanman2.Web/Views/Shared/_Sidebar.cshtml,src/Lanman2/Lanman2.Web/node_modules/**,src/Lanman2/Lanman2.Web/tsbuild.js
tsbuild.js,src/Lanman2/Lanman2.Web/Modules/Default/PurchaseOrders/PurchaseOrdersDialog.ts,src/Lanman2/Lanman2.Web/Modules/Default/PurchaseOrdersVendor/PurchaseOrdersVendorDialog.ts
# sonar.inclusions=**/*.Tests.cs
sonar.test.inclusions=src/Lanman2/Lanman2.Tests/**/*.Tests.cs
sonar.coverage.exclusions=src/Lanman2/Lanman2.Tests/**/*.cs,src/Lanman2/Lanman2.Web/Modules/**/**/*.cs,src/Lanman2/Lanman2.Web/Modules/**/**/*.ts,src/Lanman2/Lanman2.Web/Modules/**/**/**/*.tsx
sonar.cpd.exclusions=src/Lanman2/Lanman2.Web/Modules/**/**/*.ts
# Analysis Reports
sonar.javascript.lcov.reportPaths=coverage/lcov.info
# sonar.testExecutionReportPaths=reports/sonar-report.xml
sonar.qualitygate.wait=true
sonar.qualitygate.timeout=500
# Encoding
sonar.sourceEncoding=UTF-8
# Language setting
# sonar.language=cs
# Coverage report location (Cobertura format from reportgenerator)
sonar.cs.opencover.reportsPaths=TestResults/**/coverage.cobertura.xml
sonar.dotnet.visualstudio.solution.file=Lanman2.Web.sln