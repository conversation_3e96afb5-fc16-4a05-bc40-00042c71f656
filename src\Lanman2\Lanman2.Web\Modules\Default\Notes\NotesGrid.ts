﻿import { NotesColumns, NotesRow, NotesService } from '@/ServerTypes/Default';
import { Decorators, EntityGrid } from '@serenity-is/corelib';
import { NotesDialog } from './NotesDialog';

@Decorators.registerClass('Lanman2.Default.NotesGrid')
export class NotesGrid extends EntityGrid<NotesRow, any> {
    protected getColumnsKey() { return NotesColumns.columnsKey; }
    protected getDialogType() { return NotesDialog; }
    protected getRowDefinition() { return NotesRow; }
    protected getService() { return NotesService.baseUrl; }

}