{"version": 3, "sources": ["../../../../Modules/Common/ScriptInit.ts", "../../../../Modules/Common/Helpers/LanguageList.ts"], "sourcesContent": ["﻿import { EntityDialog, HtmlContentEditor } from \"@serenity-is/corelib\";\r\nimport { Authorization, Config, ErrorHandling } from \"@serenity-is/corelib\";\r\nimport { siteLanguageList } from \"./Helpers/LanguageList\";\r\n\r\nConfig.rootNamespaces.push('Lanman2');\r\nEntityDialog.defaultLanguageList = siteLanguageList;\r\nHtmlContentEditor.CKEditorBasePath = \"~/Serenity.Assets/Scripts/ckeditor/\";\r\n\r\nif ($.fn['colorbox']) {\r\n    $.fn['colorbox'].settings.maxWidth = \"95%\";\r\n    $.fn['colorbox'].settings.maxHeight = \"95%\";\r\n}\r\n\r\nwindow.onerror = ErrorHandling.runtimeErrorHandler;", "import { LanguageRow } from \"../../Administration\";\r\n\r\nexport function siteLanguageList() {\r\n    var result: string[][] = [];\r\n    for (var k of LanguageRow.getLookup().items) {\r\n        if (k.LanguageId !== 'en') {\r\n            result.push([k.Id.toString(), k.LanguageName]);\r\n        }\r\n    }\r\n    return result;\r\n}\r\n"], "mappings": "sHAAC,IAAAA,EAAgD,SACjDA,EAAqD,SCC9C,SAASC,GAAmB,CAC/B,IAAIC,EAAqB,CAAC,EAC1B,QAASC,KAAKC,EAAY,UAAU,EAAE,MAC9BD,EAAE,aAAe,MACjBD,EAAO,KAAK,CAACC,EAAE,GAAG,SAAS,EAAGA,EAAE,YAAY,CAAC,EAGrD,OAAOD,CACX,CARgBG,EAAAJ,EAAA,oBDEhB,SAAO,eAAe,KAAK,SAAS,EACpC,eAAa,oBAAsBK,EACnC,oBAAkB,iBAAmB,sCAEjC,EAAE,GAAG,WACL,EAAE,GAAG,SAAY,SAAS,SAAW,MACrC,EAAE,GAAG,SAAY,SAAS,UAAY,OAG1C,OAAO,QAAU,gBAAc", "names": ["import_corelib", "siteLanguageList", "result", "k", "LanguageRow", "__name", "siteLanguageList"]}