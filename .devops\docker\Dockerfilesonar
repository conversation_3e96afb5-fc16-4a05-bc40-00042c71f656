# Base image with .NET SDK
FROM mcr.microsoft.com/dotnet/sdk:6.0 AS base

# Install Java for sonar-scanner and other dependencies
RUN apt-get update && apt-get install -y openjdk-17-jre curl unzip \
    && curl -Lo /tmp/sonar-scanner.zip https://binaries.sonarsource.com/Distribution/sonar-scanner-cli/sonar-scanner-cli-5.0.1.3006-linux.zip \
    && unzip /tmp/sonar-scanner.zip -d /opt \
    && mv /opt/sonar-scanner-* /opt/sonar-scanner \
    && ln -s /opt/sonar-scanner/bin/sonar-scanner /usr/local/bin/sonar-scanner \
    && rm /tmp/sonar-scanner.zip

# Install Node.js, wkhtmltopdf, and other runtime dependencies
RUN apt-get update && apt-get install -y apt-transport-https \
    && curl -fsSL https://deb.nodesource.com/setup_18.x | bash - \
    && apt-get install -y nodejs \
    && apt-get install -y wget gnupg fontconfig libfreetype6 libx11-6 libxext6 \
       libxrender1 xfonts-75dpi xfonts-base wkhtmltopdf

# Set environment variables
ENV SONAR_SCANNER_OPTS="-Xmx512m"
ENV PATH="/opt/sonar-scanner/bin:$PATH"

WORKDIR /app

# Copy project files and restore dependencies
COPY ["src/Lanman2/Lanman2.Web/Lanman2.Web.csproj", "src/Lanman2/Lanman2.Web/"]
COPY ["src/Lanman2/Lanman2.Tests/Lanman2.Tests.csproj", "src/Lanman2/Lanman2.Tests/"]
COPY ["src/Lanman2/Lanman2.Web/package.json", "src/Lanman2/Lanman2.Web/"]

# Restore dependencies
RUN dotnet restore "src/Lanman2/Lanman2.Web/Lanman2.Web.csproj"
RUN dotnet restore "src/Lanman2/Lanman2.Tests/Lanman2.Tests.csproj"

# Copy the entire solution
COPY . .

# Build the web project
WORKDIR "/app/src/Lanman2/Lanman2.Web"
RUN npm install 
RUN dotnet build "Lanman2.Web.csproj" -c Release

# Run tests with code coverage collection
WORKDIR "/app/src/Lanman2/Lanman2.Tests"
RUN dotnet test "Lanman2.Tests.csproj" -c Release --collect:"XPlat Code Coverage" --results-directory /app/TestResults

# Generate code coverage report using ReportGenerator from NuGet package
RUN dotnet /root/.nuget/packages/reportgenerator/5.1.26/tools/net6.0/ReportGenerator.dll \
    -reports:/app/TestResults/**/coverage.cobertura.xml \
    -targetdir:/app/TestResults/CoverageReport \
    -reporttypes:Cobertura

# Run SonarQube analysis
WORKDIR /app
ARG SONAR_HOST_URL
ARG SONAR_KEY_EE
ARG BUILD_NUMBER
ARG GIT_BRANCH
RUN sonar-scanner \
  -Dsonar.projectKey=mi-lease-manager \
  -Dsonar.sources=src/Lanman2 \
  -Dsonar.cs.opencover.reportsPaths=/app/TestResults/**/coverage.cobertura.xml \
  -Dsonar.host.url=$SONAR_HOST_URL \
  -Dsonar.token=$SONAR_KEY_EE \
  -Dsonar.projectVersion=$BUILD_NUMBER \
  -Dsonar.branch.name=$GIT_BRANCH