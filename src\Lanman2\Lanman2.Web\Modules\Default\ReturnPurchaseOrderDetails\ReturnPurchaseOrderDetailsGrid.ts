import { ItemTypesRow, PurchaseOrderDetailsColumns, PurchaseOrderDetailsRow, PurchaseOrderDetailsService } from '@/ServerTypes/Default';
import { Criteria, Decorators, EntityGrid, indexOf, StringEditor, ToolButton } from '@serenity-is/corelib';
import { ReturnPurchaseOrderDetailsDialog } from './ReturnPurchaseOrderDetailsDialog';

const fld = PurchaseOrderDetailsRow.Fields;

@Decorators.registerClass('Lanman2.Default.ReturnPurchaseOrderDetailsGrid')
export class ReturnPurchaseOrderDetailsGrid extends EntityGrid<PurchaseOrderDetailsRow, any> {
    protected getColumnsKey() { return PurchaseOrderDetailsColumns.columnsKey; }
    protected getDialogType() { return ReturnPurchaseOrderDetailsDialog; }
    protected getRowDefinition() { return PurchaseOrderDetailsRow; }
    protected getService() { return PurchaseOrderDetailsService.baseUrl; }

    protected getInitialTitle() { return 'Return Items' }

    protected getQuickFilters() {
        let quickFilters = super.getQuickFilters();

        const currentDate = new Date();
        const month = String(currentDate.getMonth() + 1).padStart(2, '0');
        const year = String(currentDate.getFullYear());
        const formattedDate = `${month}/${year}`;

        let leaseExpiryDateShortFilter = quickFilters.find(x => x.field === fld.LeaseExpiryDateShort);
        leaseExpiryDateShortFilter && (leaseExpiryDateShortFilter.init = (w: StringEditor) => {
            w.value = formattedDate
        });

        return quickFilters.filter(x => x.field === fld.IsReturnComplete
            || x.field === fld.LeaseExpiryDateShort);
    }

    protected setCriteriaParameter() {
        super.setCriteriaParameter();

        const itemTypeDesktop = ItemTypesRow.getLookup().items.find(x => x.ItemTypeName === 'Desktop');
        const itemTypeLaptop = ItemTypesRow.getLookup().items.find(x => x.ItemTypeName === 'Laptop');

        if (itemTypeDesktop && itemTypeLaptop) {
            const itemTypeDesktopId = itemTypeDesktop.ItemTypeId;
            const itemTypeLaptopId = itemTypeLaptop.ItemTypeId;

            const criteria = this.view.params.Criteria;
            const desktopOrLaptopCriteria = Criteria.or([[fld.ItemTypeId], '=', itemTypeDesktopId], [[fld.ItemTypeId], '=', itemTypeLaptopId]);
            const serialNumberCriteria = [[fld.SerialNumber], '!=', ''];

            const isDiscoveryCompleteCriteria = [[fld.IsDiscoveryComplete], '=', 'true'];

            const noReplacementCriteria = [[fld.ReplacementType], '=', 'No Replacement'];
            const orderNewAndReplacementPOCompleteCriteria = Criteria.and([[fld.ReplacementType], '=', 'Order New'], [[fld.IsReplacementPOComplete], '=', true]);
            const returnCriteria = Criteria.or(noReplacementCriteria, orderNewAndReplacementPOCompleteCriteria);

            this.view.params.Criteria = Criteria.and(
                criteria,
                serialNumberCriteria,
                desktopOrLaptopCriteria,
                isDiscoveryCompleteCriteria,
                returnCriteria
            );
        }
    }

    protected getButtons(): ToolButton[] {
        let buttons = super.getButtons();

        buttons.splice(indexOf(buttons, x => x.action == "add"), 1);

        return buttons;
    }
}