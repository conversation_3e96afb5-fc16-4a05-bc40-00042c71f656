import { ItemTypesRow, PurchaseOrderDetailsColumns, PurchaseOrderDetailsRow, PurchaseOrderDetailsService } from '@/ServerTypes/Default';
import { Criteria, Decorators, EntityGrid, indexOf, ToolButton, Aggregators, StringEditor } from '@serenity-is/corelib';
import { GroupItemMetadataProvider } from '@serenity-is/sleekgrid';
import { PurchaseOrderDetailsDialog } from './PurchaseOrderDetailsDialog';
import { PurchaseOrderDetailExcelImportDialog } from './PurchaseOrderDetailsExcelImportPage';

const fld = PurchaseOrderDetailsRow.Fields;


@Decorators.registerClass('Lanman2.Default.PurchaseOrderDetailsGrid')
export class PurchaseOrderDetailsGrid extends EntityGrid<PurchaseOrderDetailsRow, any> {
    protected getColumnsKey() { return PurchaseOrderDetailsColumns.columnsKey; }
    protected getDialogType() { return PurchaseOrderDetailsDialog; }
    protected getRowDefinition() { return PurchaseOrderDetailsRow; }
    protected getService() { return PurchaseOrderDetailsService.baseUrl; }
    

    protected getInitialTitle() { return 'Lease Items' }
    
    protected createSlickGrid() {
        let grid = super.createSlickGrid();

        // need to register this plugin for grouping or you'll have errors
        grid.registerPlugin(new GroupItemMetadataProvider());

        this.view.setSummaryOptions({
            aggregators: [
                new Aggregators.Sum('UnitsInStock'),
            ]
        });

        return grid;
    }

    protected getSlickOptions() {
        let opt = super.getSlickOptions();
        opt.showFooterRow = true;
        return opt;
    }

    protected getButtons(): ToolButton[] {
        let buttons = super.getButtons();

        buttons.splice(indexOf(buttons, x => x.action == "add"), 1);

        // add our import button
        buttons.push({
            title: 'Import From Excel',
            cssClass: 'export-xlsx-button',
            separator: true,
            onClick: () => {
                // open import dialog, let it handle rest
                let dialog = new PurchaseOrderDetailExcelImportDialog();
                dialog.element.on('dialogclose', () => {
                    this.refresh();
                    dialog = null;
                });
                dialog.dialogOpen();
            }
        });

        return buttons;
    }
     
    protected setCriteriaParameter() {
        super.setCriteriaParameter();

        let itemTypeDesktopId = ItemTypesRow.getLookup().items
            .filter(x => x.ItemTypeName === 'Desktop')[0].ItemTypeId;

        let itemTypeLaptopId = ItemTypesRow.getLookup().items
            .filter(x => x.ItemTypeName === 'Laptop')[0].ItemTypeId;

        this.view.params.Criteria = Criteria.and(this.view.params.Criteria, [[fld.SerialNumber], '!=', ''], (Criteria.or([[fld.ItemTypeId], '=', itemTypeDesktopId], [[fld.ItemTypeId], '=', itemTypeLaptopId])));
    }

    protected getQuickFilters() {
        let quickFilters = super.getQuickFilters();

        const currentDate = new Date();
        const month = String(currentDate.getMonth() + 1).padStart(2, '0');
        const year = String(currentDate.getFullYear());
        const formattedDate = `${month}/${year}`;

        let leaseExpiryDateShortFilter = quickFilters.find(x => x.field === fld.LeaseExpiryDateShort);
        leaseExpiryDateShortFilter && (leaseExpiryDateShortFilter.init = (w: StringEditor) => {
            w.value = formattedDate
        });

        return quickFilters.filter(x => x.field === fld.IsDiscoveryComplete
            || x.field === fld.LeaseExpiryDateShort);
    }

    protected getColumns() {
        let columns = super.getColumns();

        columns.splice(indexOf(columns, x => x.field == "IsReOrderInfoGatheringComplete"), 1);
        columns.splice(indexOf(columns, x => x.field == "IsReplacementPOComplete"), 1);
        columns.splice(indexOf(columns, x => x.field == "ReturnDate"), 1);
        columns.splice(indexOf(columns, x => x.field == "ReturnEmailDate"), 1);
        columns.splice(indexOf(columns, x => x.field == "ReturnTrackingNumber"), 1);
        columns.splice(indexOf(columns, x => x.field == "ItemDisabledDate"), 1);
        columns.splice(indexOf(columns, x => x.field == "IsReturnComplete"), 1);


        return columns;
    }
    
}