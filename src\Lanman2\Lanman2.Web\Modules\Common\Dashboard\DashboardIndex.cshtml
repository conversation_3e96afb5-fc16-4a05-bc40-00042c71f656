@model Lanman2.Common.DashboardPageModel
@inject ITextLocalizer Localizer
@{
	ViewData["Title"] = "Dashboard";
	ViewData["PageId"] = "Dashboard";
}

@section Head {
	@Html.StyleBundle("Pages/Dashboard")
	@Html.ScriptBundle("Pages/Dashboard")
}

@section ContentHeader {
	<h1>@Localizer.Get("Navigation.Dashboard")</h1>
}

<style>
	.s-dashboard-card, .s-dashboard-card-sm {
		margin-bottom: var(--bs-gutter-x);
	}

		.s-dashboard-card .card-header {
			padding: 1rem;
			border-bottom: none;
			background-color: transparent;
		}

		.s-dashboard-card .card-body {
			border-radius: 0 0 4px 4px;
			padding: 1rem;
		}

		.s-dashboard-card .card-footer {
			border-top-color: transparent;
			background-color: transparent;
			padding: 1rem;
		}

		.s-dashboard-card .card-title {
			margin: 0;
			font-size: 1.4rem;
			color: var(--s-table-title);
		}

		.s-dashboard-card p {
			font-size: 16px;
			color: #333;
			font-weight: bold;
		}

		.s-dashboard-card-sm .card-body {
			padding: 1rem;
			border-radius: 4px 4px 0 0;
			margin: 1px;
		}

		.s-dashboard-card-sm .card-footer {
			color: rgba(var(--s-bright-rgb), 0.8);
			background-color: var(--bs-body-bg);
			border-radius: 0 0 4px 4px;
			border-top: none;
			margin: 0 1px 1px 1px;
			padding: 3px 0;
			text-align: center;
			text-decoration: none;
		}

		.s-dashboard-card-sm h3 {
			font-size: 38px;
			font-weight: bold;
			margin: 0 0 10px 0;
			white-space: nowrap;
			padding: 0;
		}

		.s-dashboard-card-sm p {
			font-size: 15px;
		}

			.s-dashboard-card-sm p > small {
				display: block;
				font-size: 13px;
				margin-top: 5px;
			}

		.s-dashboard-card-sm .icon {
			transition: all 0.3s linear;
			position: absolute;
			top: 13px;
			color: #fff;
			opacity: 0.9;
			right: 10px;
			font-size: 64px;
		}

	.bg-world-map {
		background: #cf697f;
	}

	.s-dashboard-card-sm:hover .icon {
		font-size: 70px;
	}

	.hideCalendar .ui-datepicker-calendar {
		display: none;
	}

	.ui-datepicker table.ui-datepicker-calendar {
		display: none;
	}
	.lease-expiry, .item-name, .total, .discovery-complete, .return-complete {
		width: 100px;
	}
	.item-description {
		width: 400px;
	}
	.reorder-info, .replacement-po {
		width: 130px;
	}
</style>




<div class="row">
	<div class="card s-dashboard-card ">

		<p>Lease Expiry Date:</p>

		<div name="expiryDate" id="expiryDate"></div>


	</div>
</div>
<div class="row">
	<div class="card s-dashboard-card ">
		@* <div class="card-body"> *@
		<div class="table-responsive">
			<table class="table table-striped table-hover table-sm" id="allLeaseItems" aria-describedby="allLeaseItemsDesc">
				<caption id="allLeaseItemsDesc">
					This table displays a summary of lease items, including expiry dates and completion status.
				</caption>
				<thead>
					<tr>
						<th>Lease Expiry Date</th>
						<th>Total Devices</th>
						<th>Discovery Complete</th>
						<th>ReOrder Info Gathering Complete</th>
						<th>Replacement PO Complete</th>
						<th>Return Complete</th>
					</tr>
				</thead>
				<tbody>
					@foreach (var item in Model.LeaseItems)
					{
						<tr>
							<td>@item.LeaseExpiryDate.ToShortDateString()</td>
							<td>@item.Total</td>
							<td>@item.IsDiscoveryComplete</td>
							<td>@item.IsReOrderInfoGatheringComplete</td>
							<td>@item.IsReplacementPOComplete</td>
							<td>@item.IsReturnComplete</td>
						</tr>
					}
				</tbody>
			</table>
		</div>
	</div>
</div>

<div class="row">
	<div class="card s-dashboard-card overflow-auto">
		<div class="table-responsive">
			<table class="table table-striped table-hover table-sm" id="leaseItemsForSelectedMonth" aria-describedby="leaseItemsForSelectedMonthDesc">
				<caption id="leaseItemsForSelectedMonthDesc">
					This table displays lease items for the selected month, including expiry dates, item types, descriptions, and completion statuses.
				</caption>
				<thead>
					<tr>
						<th>Lease Expiry Date</th>
						<th>Item Type</th>
						<th>Item Description</th>
						<th>Total Devices</th>
						<th>Discovery Complete</th>
						<th>ReOrder Info Complete</th>
						<th>Replacement PO Complete</th>
						<th>Return Complete</th>
					</tr>
				</thead>
				<tbody>
					@{
						var filteredLeaseItems = Model.DesktopsAndLaptops;
						@* .Where(a => a.LeaseExpiryDate >= new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1)
        && a.LeaseExpiryDate <= new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.DaysInMonth(DateTime.Now.Year, DateTime.Now.Month)))
        .OrderBy(a => a.ItemName); *@
						foreach (var item in filteredLeaseItems)
						{
							<tr>
								<td class="lease-expiry">@item.LeaseExpiryDate.ToShortDateString()</td>
								<td class="item-name">@item.ItemName</td>
								<td class="item-description">@item.ItemDescription</td>
								<td class="total">@item.Total</td>
								<td class="discovery-complete">@item.IsDiscoveryComplete</td>
								<td class="reorder-info">@item.IsReOrderInfoGatheringComplete</td>
								<td class="replacement-po">@item.IsReplacementPOComplete</td>
								<td class="return-complete">@item.IsReturnComplete</td>
							</tr>
						}

					}

				</tbody>
			</table>
		</div>

	</div>
</div>

<script src="https://code.jquery.com/jquery-1.10.2.js" integrity="sha256-it5nQKHTz+34HijZJQkpNBIHsjpV8b6QzMJs9tmOBSo=" crossorigin="anonymous"></script>
<script src="https://code.jquery.com/ui/1.10.4/jquery-ui.js" integrity="sha256-tp8VZ4Y9dg702r7D6ynzSavKSwB9zjariSZ4Snurvmw=" crossorigin="anonymous"></script>

<script>

	$(function () {
		//filter the above tables on date selected in calendar
		//display a sample alert


		$("#expiryDate").datepicker({
			changeMonth: true,
			changeYear: true,
			onChangeMonthYear: function (year, month, inst) {
				var selectedDate = new Date(year, month);
				var newDate = new Date(selectedDate.getFullYear(), selectedDate.getMonth(), selectedDate.getDate() - 1);
				filterTableByDate(newDate);
				filterLeaseItemsForSelectedMonth(newDate);
				$(this).datepicker("setDate", newDate);
			},
			// beforeShow: function () {
			//     $("#ui-datepicker-div").addClass("hideCalendar");
			// },
			// onClose: function () {
			//     $("#ui-datepicker-div").removeClass("hideCalendar");
			// }
		});



		function filterTableByDate(selectedDate) {
			var tableRows = $("#allLeaseItems tbody tr");
			tableRows.each(function () {
				var leaseExpiryDate = new Date($(this).find("td:first-child").text());

				var sixMonthsAfterSelectedDate = new Date(selectedDate.getFullYear(), selectedDate.getMonth() + 6, selectedDate.getDate());
				var sixMonthsBeforeSelectedDate = new Date(selectedDate.getFullYear(), selectedDate.getMonth() - 6, selectedDate.getDate());

				if (leaseExpiryDate >= sixMonthsBeforeSelectedDate && leaseExpiryDate <= sixMonthsAfterSelectedDate) {
					$(this).show();
				} else {
					$(this).hide();
				}
			});
		}

		function filterLeaseItemsForSelectedMonth(selectedDate) {
			var tableRows = $("#leaseItemsForSelectedMonth tbody tr");
			tableRows.each(function () {
				var leaseExpiryDate = new Date($(this).find("td:first-child").text());

				if (leaseExpiryDate.toDateString() === selectedDate.toDateString()) {
					$(this).show();
				} else {
					$(this).hide();
				}
			});
		}

		$(document).ready(function () {
			$("#datepicker").trigger("onChangeMonthYear", [new Date().getFullYear(), new Date().getMonth()]); // Replace with desired year and month

		});
	});
</script>