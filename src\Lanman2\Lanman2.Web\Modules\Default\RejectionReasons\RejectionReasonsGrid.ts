﻿import { RejectionReasonsColumns, RejectionReasonsRow, RejectionReasonsService } from '@/ServerTypes/Default';
import { Decorators, EntityGrid } from '@serenity-is/corelib';
import { RejectionReasonsDialog } from './RejectionReasonsDialog';

@Decorators.registerClass('Lanman2.Default.RejectionReasonsGrid')
export class RejectionReasonsGrid extends EntityGrid<RejectionReasonsRow, any> {
    protected getColumnsKey() { return RejectionReasonsColumns.columnsKey; }
    protected getDialogType() { return RejectionReasonsDialog; }
    protected getRowDefinition() { return RejectionReasonsRow; }
    protected getService() { return RejectionReasonsService.baseUrl; }

}