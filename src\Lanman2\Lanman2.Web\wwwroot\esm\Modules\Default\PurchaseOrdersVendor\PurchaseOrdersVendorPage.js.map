{"version": 3, "sources": ["../../../../../Modules/Default/PurchaseOrdersVendor/PurchaseOrdersVendorPage.ts", "../../../../../Modules/Default/PurchaseOrdersVendor/PurchaseOrdersVendorGrid.ts", "../../../../../Modules/Default/PurchaseOrdersVendor/PurchaseOrdersVendorDialog.ts"], "sourcesContent": ["import { initFullHeightGridPage } from '@serenity-is/corelib';\r\nimport { PurchaseOrdersVendorGrid } from './PurchaseOrdersVendorGrid';\r\n\r\nexport default function pageInit() {\r\n    initFullHeightGridPage(new PurchaseOrdersVendorGrid($('#GridDiv')).element);\r\n}", "import { PurchaseOrdersColumns, PurchaseOrdersRow, PurchaseOrdersService, PurchaseOrdersVendorColumns, PurchaseOrdersVendorRow, PurchaseOrdersVendorService } from '@/ServerTypes/Default';\r\nimport { Criteria, Decorators, EntityGrid, indexOf, StringEditor } from '@serenity-is/corelib';\r\nimport { ReportHelper } from \"@serenity-is/extensions\";\r\nimport { PurchaseOrdersVendorDialog } from './PurchaseOrdersVendorDialog';\r\n\r\n\r\nconst fld = PurchaseOrdersRow.Fields;\r\n\r\**************************('Lanman2.Default.PurchaseOrdersVendorGrid')\r\nexport class PurchaseOrdersVendorGrid extends EntityGrid<PurchaseOrdersVendorRow, any> {\r\n    protected getColumnsKey() { return PurchaseOrdersVendorColumns.columnsKey; }\r\n    protected getDialogType() { return PurchaseOrdersVendorDialog; }\r\n    protected getRowDefinition() { return PurchaseOrdersVendorRow; }\r\n    protected getService() { return PurchaseOrdersVendorService.baseUrl; }\r\n\r\n    constructor(container: JQuery) {\r\n        super(container);\r\n    }\r\n\r\n    protected setCriteriaParameter() {\r\n        super.setCriteriaParameter();\r\n\r\n        const criteria = this.view.params.Criteria;\r\n\r\n        this.view.params.Criteria = Criteria.and(\r\n            criteria,\r\n            [[fld.PurchaseOrderNo], '!=', '']\r\n        );\r\n    }\r\n\r\n    protected getButtons() {\r\n        var buttons = super.getButtons();\r\n\r\n        buttons.splice(indexOf(buttons, x => x.title == \"New Purchase Orders\"), 1);\r\n        return buttons;\r\n    }\r\n\r\n    protected getColumns() {\r\n        var columns = super.getColumns();\r\n        \r\n\r\n\r\n        return columns;\r\n    }\r\n\r\n    protected onClick(e: JQueryEventObject, row: number, cell: number) {\r\n        super.onClick(e, row, cell);\r\n\r\n        if (e.isDefaultPrevented())\r\n            return;\r\n\r\n        var item = this.itemAt(row);\r\n\r\n        var target = $(e.target);\r\n\r\n        // if user clicks \"i\" element, e.g. icon\r\n        if (target.parent().hasClass('inline-action'))\r\n            target = target.parent();\r\n\r\n        if (target.hasClass('inline-action')) {\r\n            e.preventDefault();\r\n\r\n            if (target.hasClass('print-invoice')) {\r\n                ReportHelper.execute({\r\n                    reportKey: 'PurchaseOrderReport',\r\n                    params: {\r\n                        OrderID: item.PurchaseOrderId\r\n                    }\r\n                });\r\n            }\r\n        }\r\n    }\r\n\r\n    protected getQuickFilters() {\r\n        var quickFilters = super.getQuickFilters();\r\n\r\n        const currentDate = new Date();\r\n        const month = String(currentDate.getMonth() + 1).padStart(2, '0');\r\n        const year = String(currentDate.getFullYear());\r\n        const formattedDate = `${month}/${year}`;\r\n\r\n        let purchaseOrderSimpleCreatedDateFilter = quickFilters.find(x => x.field === fld.PurchaseOrderSimpleCreatedDate);\r\n\r\n\r\n        purchaseOrderSimpleCreatedDateFilter && (purchaseOrderSimpleCreatedDateFilter.init = (w: StringEditor) => {\r\n            w.value = formattedDate\r\n        });\r\n\r\n        return quickFilters;\r\n    }\r\n}\r\n", "import { CostCentersRow, ItemsRow, PurchaseOrderDetailsRow, PurchaseOrderDetailsService, PurchaseOrdersForm, PurchaseOrdersRow, PurchaseOrdersService, PurchaseOrdersVendorForm, PurchaseOrdersVendorRow, PurchaseOrdersVendorService, ShippingAddressesRow } from '@/ServerTypes/Default';\r\nimport { Authorization, Decorators, EditorUtils, EntityDialog, indexOf, reloadLookup, serviceRequest, toId } from '@serenity-is/corelib';\r\nimport { ReportHelper } from \"@serenity-is/extensions\";\r\n\r\**************************('Lanman2.Default.PurchaseOrdersVendorDialog')\r\nexport class PurchaseOrdersVendorDialog extends EntityDialog<PurchaseOrdersVendorRow, any> {\r\n    protected getFormKey() { return PurchaseOrdersVendorForm.formKey; }\r\n    protected getRowDefinition() { return PurchaseOrdersVendorRow; }\r\n    protected getService() { return PurchaseOrdersVendorService.baseUrl; }\r\n\r\n    protected form = new PurchaseOrdersVendorForm(this.idPrefix);\r\n    oldPurchaseOrderId: number;\r\n\r\n    constructor(oldPurchaseOrderIdParam: number) {\r\n        super();\r\n        this.oldPurchaseOrderId = oldPurchaseOrderIdParam;\r\n\r\n        \r\n    }\r\n\r\n    protected afterLoadEntity() {\r\n        super.afterLoadEntity();\r\n\r\n        this.form.IsReOrderInfoGatheringComplete.getGridField().toggle(false);\r\n        this.form.IsReplacementPOComplete.getGridField().toggle(false);\r\n\r\n        //if (Authorization.hasPermission(\"Administration:POVendor\")) {\r\n\r\n        //    var user = Authorization.userDefinition;\r\n\r\n            //$(\".s-Toolbar\").hide();\r\n        //    //EditorUtils.setReadonly(this.element.find('.editor'), true);\r\n        //}\r\n    \r\n\r\n        if (!this.entity.PurchaseOrderNo) {\r\n            var costCenterId = toId(this.entity.CostCenterId);\r\n            if (costCenterId) {\r\n\r\n                let dateNow = new Date();\r\n                CostCentersRow.getLookupAsync().then(a => {\r\n                    var costCenter = a.itemById[costCenterId].CostCenterName;\r\n                    var month = (dateNow.getMonth() + 1).toString().padStart(2, '0');\r\n                    var day = dateNow.getDate().toString().padStart(2, '0');\r\n\r\n                    var costCenterPO = costCenter + \"-\" + month + day + dateNow.getFullYear().toString().substring(2);\r\n                    PurchaseOrdersVendorRow.getLookupAsync().then(b => {\r\n                        var purchaseOrderCount = b.items.filter(x => x.PurchaseOrderNo && x.PurchaseOrderNo.startsWith(costCenterPO)).length;\r\n\r\n                        this.form.PurchaseOrderNo.value = costCenterPO + \"-\" + (purchaseOrderCount + 1);\r\n                    });\r\n                });\r\n            }\r\n        }\r\n    }\r\n\r\n    protected updateInterface() {\r\n        super.updateInterface();\r\n   \r\n            EditorUtils.setReadonly(this.form.ShippingAddressesAddress.element, true);\r\n            EditorUtils.setReadonly(this.form.ShippingAddressesCityName.element, true);\r\n            EditorUtils.setReadonly(this.form.ShippingAddressesStateName.element, true);\r\n            EditorUtils.setReadonly(this.form.ShippingAddressesZIPCode.element, true);\r\n        EditorUtils.setReadonly(this.form.PurchaseOrderNo.element, true);\r\n        EditorUtils.setReadonly(this.form.AssignedOwnerEmployeeId.element, true);\r\n        EditorUtils.setReadonly(this.form.CostCenterId.element, true);\r\n        EditorUtils.setReadonly(this.form.ShippingAttention.element, true);\r\n        EditorUtils.setReadonly(this.form.ShippingAddressesId.element, true);\r\n        EditorUtils.setReadonly(this.form.PurchaseOrderCreatedDate.element, true);\r\n        EditorUtils.setReadonly(this.form.PurchaseOrderEmailSentOnDate.element, true);\r\n        EditorUtils.setReadonly(this.form.PurchaseOrderNotes.element, true);\r\n        EditorUtils.setReadonly(this.form.VendorId.element, true);\r\n        EditorUtils.setReadonly(this.form.IsReOrderInfoGatheringComplete.element, true);\r\n        EditorUtils.setReadonly(this.form.IsReplacementPOComplete.element, true);\r\n      \r\n    }\r\n\r\n    onSaveSuccess(response) {\r\n        reloadLookup(PurchaseOrdersVendorRow.lookupKey);\r\n        reloadLookup(PurchaseOrderDetailsRow.lookupKey);\r\n        var entityId = response.EntityId;\r\n        //if oldPurchaseId is empty\r\n        if (this.oldPurchaseOrderId > 0) {\r\n\r\n            (PurchaseOrdersVendorRow.getLookupAsync()).then(a => {\r\n                var oldPurchaseOrder = a.itemById[this.oldPurchaseOrderId];\r\n\r\n                //check if IsReOrderInfoGatheringComplete is not hidden\r\n                if (this.form.IsReOrderInfoGatheringComplete && !this.form.IsReOrderInfoGatheringComplete.element[0].hidden) {\r\n                        oldPurchaseOrder.IsReOrderInfoGatheringComplete = this.form.IsReOrderInfoGatheringComplete?.value;\r\n                }\r\n\r\n                //check if IsReplacementPOComplete is not hidden\r\n                if (this.form.IsReplacementPOComplete && !this.form.IsReplacementPOComplete.element[0].hidden) {\r\n                    oldPurchaseOrder.IsReplacementPOComplete = this.form.IsReplacementPOComplete?.value;\r\n                }\r\n\r\n                (PurchaseOrderDetailsRow.getLookupAsync()).then(a => {\r\n\r\n                    var purchaseOrderDetails = a.items.filter(x => x.PurchaseOrderId === this.oldPurchaseOrderId\r\n                        && (x.ItemTypeName === 'Desktop' || x.ItemTypeName === 'Laptop'));\r\n\r\n                    if (!purchaseOrderDetails[0].ReplacementPurchaseOrderId || purchaseOrderDetails[0].ReplacementPurchaseOrderId < 0) {\r\n\r\n                        purchaseOrderDetails[0].ReplacementPurchaseOrderId = entityId;\r\n\r\n                        serviceRequest(PurchaseOrderDetailsService.Methods.Update, {\r\n                            EntityId: purchaseOrderDetails[0].PurchaseOrderDetailId,\r\n                            Entity: purchaseOrderDetails[0]\r\n                        }, (res) => {\r\n                        });\r\n                    }\r\n                });\r\n                \r\n                (PurchaseOrdersVendorRow.getLookupAsync()).then(i => {\r\n                    \r\n                    var replacementPurchaseOrder = i.itemById[entityId];\r\n                    replacementPurchaseOrder.IsReOrderInfoGatheringComplete = false;\r\n                    replacementPurchaseOrder.IsReplacementPOComplete = false;\r\n\r\n                    serviceRequest(PurchaseOrdersService.Methods.Update, {\r\n                        EntityId: entityId,\r\n                        Entity: replacementPurchaseOrder\r\n                    }, (replacementPOSaveResponse) => {\r\n\r\n                    });\r\n                });\r\n\r\n                //oldPurchaseOrder.PurchaseOrderDetailsList?.forEach(a => a.ReplacementPurchaseOrderId = response.EntityId);\r\n\r\n                serviceRequest(PurchaseOrdersVendorService.Methods.Update, {\r\n                    EntityId: this.oldPurchaseOrderId,\r\n                    Entity: oldPurchaseOrder\r\n                }, (poUpdateResponse) => {\r\n\r\n\r\n                    //PurchaseOrdersService.Update()\r\n                    //this.form.ItemTypeName.value = item.ItemTypeName;\r\n\r\n                    //var unit = (await UnitsRow.getLookupAsync()).itemById[item.UnitId];\r\n                    //this.form.UnitName.value = item.UnitName;\r\n                    //this.form.UnitPrice.value = item.UnitPrice;\r\n                });\r\n            });\r\n        }\r\n    }\r\n\r\n    getToolbarButtons() {\r\n        var buttons = super.getToolbarButtons();\r\n\r\n        buttons.splice(indexOf(buttons, x => x.title == \"Delete\"), 1);\r\n        //buttons.splice(indexOf(buttons, x => x.title == \"New Purchase Orders\"), 1);\r\n\r\n        return buttons;\r\n    }\r\n}"], "mappings": "wMAAA,IAAAA,EAAuC,SCCvC,IAAAC,EAAwE,SACxEC,EAA6B,SCD7B,IAAAC,EAAkH,SAI3G,IAAMC,EAAN,cAAyC,cAA2C,CAQvF,YAAYC,EAAiC,CACzC,MAAM,EAJV,KAAU,KAAO,IAAIC,EAAyB,KAAK,QAAQ,EAKvD,KAAK,mBAAqBD,CAG9B,CAZU,YAAa,CAAE,OAAOC,EAAyB,OAAS,CACxD,kBAAmB,CAAE,OAAOC,CAAyB,CACrD,YAAa,CAAE,OAAOC,EAA4B,OAAS,CAY3D,iBAAkB,CAexB,GAdA,MAAM,gBAAgB,EAEtB,KAAK,KAAK,+BAA+B,aAAa,EAAE,OAAO,EAAK,EACpE,KAAK,KAAK,wBAAwB,aAAa,EAAE,OAAO,EAAK,EAWzD,CAAC,KAAK,OAAO,gBAAiB,CAC9B,IAAIC,KAAe,QAAK,KAAK,OAAO,YAAY,EAChD,GAAIA,EAAc,CAEd,IAAIC,EAAU,IAAI,KAClBC,EAAe,eAAe,EAAE,KAAKC,GAAK,CACtC,IAAIC,EAAaD,EAAE,SAASH,CAAY,EAAE,eACtCK,GAASJ,EAAQ,SAAS,EAAI,GAAG,SAAS,EAAE,SAAS,EAAG,GAAG,EAC3DK,EAAML,EAAQ,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAG,GAAG,EAElDM,EAAeH,EAAa,IAAMC,EAAQC,EAAML,EAAQ,YAAY,EAAE,SAAS,EAAE,UAAU,CAAC,EAChGH,EAAwB,eAAe,EAAE,KAAKU,GAAK,CAC/C,IAAIC,EAAqBD,EAAE,MAAM,OAAOE,GAAKA,EAAE,iBAAmBA,EAAE,gBAAgB,WAAWH,CAAY,CAAC,EAAE,OAE9G,KAAK,KAAK,gBAAgB,MAAQA,EAAe,KAAOE,EAAqB,EACjF,CAAC,CACL,CAAC,CACL,CACJ,CACJ,CAEU,iBAAkB,CACxB,MAAM,gBAAgB,EAElB,cAAY,YAAY,KAAK,KAAK,yBAAyB,QAAS,EAAI,EACxE,cAAY,YAAY,KAAK,KAAK,0BAA0B,QAAS,EAAI,EACzE,cAAY,YAAY,KAAK,KAAK,2BAA2B,QAAS,EAAI,EAC1E,cAAY,YAAY,KAAK,KAAK,yBAAyB,QAAS,EAAI,EAC5E,cAAY,YAAY,KAAK,KAAK,gBAAgB,QAAS,EAAI,EAC/D,cAAY,YAAY,KAAK,KAAK,wBAAwB,QAAS,EAAI,EACvE,cAAY,YAAY,KAAK,KAAK,aAAa,QAAS,EAAI,EAC5D,cAAY,YAAY,KAAK,KAAK,kBAAkB,QAAS,EAAI,EACjE,cAAY,YAAY,KAAK,KAAK,oBAAoB,QAAS,EAAI,EACnE,cAAY,YAAY,KAAK,KAAK,yBAAyB,QAAS,EAAI,EACxE,cAAY,YAAY,KAAK,KAAK,6BAA6B,QAAS,EAAI,EAC5E,cAAY,YAAY,KAAK,KAAK,mBAAmB,QAAS,EAAI,EAClE,cAAY,YAAY,KAAK,KAAK,SAAS,QAAS,EAAI,EACxD,cAAY,YAAY,KAAK,KAAK,+BAA+B,QAAS,EAAI,EAC9E,cAAY,YAAY,KAAK,KAAK,wBAAwB,QAAS,EAAI,CAE3E,CAEA,cAAcE,EAAU,IACpB,gBAAab,EAAwB,SAAS,KAC9C,gBAAac,EAAwB,SAAS,EAC9C,IAAIC,EAAWF,EAAS,SAEpB,KAAK,mBAAqB,GAEzBb,EAAwB,eAAe,EAAG,KAAKK,GAAK,CApFjE,IAAAW,EAAAC,EAqFgB,IAAIC,EAAmBb,EAAE,SAAS,KAAK,kBAAkB,EAGrD,KAAK,KAAK,gCAAkC,CAAC,KAAK,KAAK,+BAA+B,QAAQ,CAAC,EAAE,SAC7Fa,EAAiB,gCAAiCF,EAAA,KAAK,KAAK,iCAAV,YAAAA,EAA0C,OAIhG,KAAK,KAAK,yBAA2B,CAAC,KAAK,KAAK,wBAAwB,QAAQ,CAAC,EAAE,SACnFE,EAAiB,yBAA0BD,EAAA,KAAK,KAAK,0BAAV,YAAAA,EAAmC,OAGjFH,EAAwB,eAAe,EAAG,KAAKT,GAAK,CAEjD,IAAIc,EAAuBd,EAAE,MAAM,OAAOO,GAAKA,EAAE,kBAAoB,KAAK,qBAClEA,EAAE,eAAiB,WAAaA,EAAE,eAAiB,SAAS,GAEhE,CAACO,EAAqB,CAAC,EAAE,4BAA8BA,EAAqB,CAAC,EAAE,2BAA6B,KAE5GA,EAAqB,CAAC,EAAE,2BAA6BJ,KAErD,kBAAeK,EAA4B,QAAQ,OAAQ,CACvD,SAAUD,EAAqB,CAAC,EAAE,sBAClC,OAAQA,EAAqB,CAAC,CAClC,EAAIE,GAAQ,CACZ,CAAC,EAET,CAAC,EAEArB,EAAwB,eAAe,EAAG,KAAKsB,GAAK,CAEjD,IAAIC,EAA2BD,EAAE,SAASP,CAAQ,EAClDQ,EAAyB,+BAAiC,GAC1DA,EAAyB,wBAA0B,MAEnD,kBAAeC,EAAsB,QAAQ,OAAQ,CACjD,SAAUT,EACV,OAAQQ,CACZ,EAAIE,GAA8B,CAElC,CAAC,CACL,CAAC,KAID,kBAAexB,EAA4B,QAAQ,OAAQ,CACvD,SAAU,KAAK,mBACf,OAAQiB,CACZ,EAAIQ,GAAqB,CASzB,CAAC,CACL,CAAC,CAET,CAEA,mBAAoB,CAChB,IAAIC,EAAU,MAAM,kBAAkB,EAEtC,OAAAA,EAAQ,UAAO,WAAQA,EAASf,GAAKA,EAAE,OAAS,QAAQ,EAAG,CAAC,EAGrDe,CACX,CACJ,EAtJ2FC,EAAA/B,EAAA,8BAA9EA,EAANgC,EAAA,CADN,aAAW,cAAc,4CAA4C,GACzDhC,GDCb,IAAMiC,EAAMC,EAAkB,OAGjBC,EAAN,cAAuC,YAAyC,CACzE,eAAgB,CAAE,OAAOC,EAA4B,UAAY,CACjE,eAAgB,CAAE,OAAOC,CAA4B,CACrD,kBAAmB,CAAE,OAAOC,CAAyB,CACrD,YAAa,CAAE,OAAOC,EAA4B,OAAS,CAErE,YAAYC,EAAmB,CAC3B,MAAMA,CAAS,CACnB,CAEU,sBAAuB,CAC7B,MAAM,qBAAqB,EAE3B,IAAMC,EAAW,KAAK,KAAK,OAAO,SAElC,KAAK,KAAK,OAAO,SAAW,WAAS,IACjCA,EACA,CAAC,CAACR,EAAI,eAAe,EAAG,KAAM,EAAE,CACpC,CACJ,CAEU,YAAa,CACnB,IAAIS,EAAU,MAAM,WAAW,EAE/B,OAAAA,EAAQ,UAAO,WAAQA,EAASC,GAAKA,EAAE,OAAS,qBAAqB,EAAG,CAAC,EAClED,CACX,CAEU,YAAa,CACnB,IAAIE,EAAU,MAAM,WAAW,EAI/B,OAAOA,CACX,CAEU,QAAQC,EAAsBC,EAAaC,EAAc,CAG/D,GAFA,MAAM,QAAQF,EAAGC,EAAKC,CAAI,EAEtB,CAAAF,EAAE,mBAAmB,EAGzB,KAAIG,EAAO,KAAK,OAAOF,CAAG,EAEtBG,EAAS,EAAEJ,EAAE,MAAM,EAGnBI,EAAO,OAAO,EAAE,SAAS,eAAe,IACxCA,EAASA,EAAO,OAAO,GAEvBA,EAAO,SAAS,eAAe,IAC/BJ,EAAE,eAAe,EAEbI,EAAO,SAAS,eAAe,GAC/B,eAAa,QAAQ,CACjB,UAAW,sBACX,OAAQ,CACJ,QAASD,EAAK,eAClB,CACJ,CAAC,GAGb,CAEU,iBAAkB,CACxB,IAAIE,EAAe,MAAM,gBAAgB,EAEzC,IAAMC,EAAc,IAAI,KAClBC,EAAQ,OAAOD,EAAY,SAAS,EAAI,CAAC,EAAE,SAAS,EAAG,GAAG,EAC1DE,EAAO,OAAOF,EAAY,YAAY,CAAC,EACvCG,EAAgB,GAAGF,CAAK,IAAIC,CAAI,GAElCE,EAAuCL,EAAa,KAAKP,GAAKA,EAAE,QAAUV,EAAI,8BAA8B,EAGhH,OAAAsB,IAAyCA,EAAqC,KAAQC,GAAoB,CACtGA,EAAE,MAAQF,CACd,GAEOJ,CACX,CACJ,EAjFuFO,EAAAtB,EAAA,4BAA1EA,EAANuB,EAAA,CADN,aAAW,cAAc,0CAA0C,GACvDvB,GDNE,SAARwB,GAA4B,IAC/B,0BAAuB,IAAIC,EAAyB,EAAE,UAAU,CAAC,EAAE,OAAO,CAC9E,CAFwBC,EAAAF,EAAA", "names": ["import_corelib", "import_corelib", "import_extensions", "import_corelib", "PurchaseOrdersVendorDialog", "oldPurchaseOrderIdParam", "PurchaseOrdersVendorForm", "PurchaseOrdersVendorRow", "PurchaseOrdersVendorService", "costCenterId", "dateNow", "CostCentersRow", "a", "costCenter", "month", "day", "costCenterPO", "b", "purchaseOrderCount", "x", "response", "PurchaseOrderDetailsRow", "entityId", "_a", "_b", "oldPurchaseOrder", "purchaseOrderDetails", "PurchaseOrderDetailsService", "res", "i", "replacementPurchaseOrder", "PurchaseOrdersService", "replacementPOSaveResponse", "poUpdateResponse", "buttons", "__name", "__decorateClass", "fld", "PurchaseOrdersRow", "PurchaseOrdersVendorGrid", "PurchaseOrdersVendorColumns", "PurchaseOrdersVendorDialog", "PurchaseOrdersVendorRow", "PurchaseOrdersVendorService", "container", "criteria", "buttons", "x", "columns", "e", "row", "cell", "item", "target", "quickFilters", "currentDate", "month", "year", "formattedDate", "purchaseOrderSimpleCreatedDateFilter", "w", "__name", "__decorateClass", "pageInit", "PurchaseOrdersVendorGrid", "__name"]}