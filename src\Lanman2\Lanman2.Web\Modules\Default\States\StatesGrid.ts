﻿import { StatesColumns, StatesRow, StatesService } from '@/ServerTypes/Default';
import { Decorators, EntityGrid } from '@serenity-is/corelib';
import { StatesDialog } from './StatesDialog';

@Decorators.registerClass('Lanman2.Default.StatesGrid')
export class StatesGrid extends EntityGrid<StatesRow, any> {
    protected getColumnsKey() { return StatesColumns.columnsKey; }
    protected getDialogType() { return StatesDialog; }
    protected getRowDefinition() { return StatesRow; }
    protected getService() { return StatesService.baseUrl; }

}