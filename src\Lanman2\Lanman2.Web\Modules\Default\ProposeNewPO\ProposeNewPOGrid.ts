import { ItemsRow, ItemTypesRow, PurchaseOrderDetailsColumns, PurchaseOrderDetailsRow, PurchaseOrderDetailsService, PurchaseOrdersRow } from '@/ServerTypes/Default';
import { Criteria, Decorators, EntityGrid, indexOf, ToolButton, deepClone, StringEditor } from '@serenity-is/corelib';
import { PurchaseOrderDetailsDialog } from '../PurchaseOrderDetails/PurchaseOrderDetailsDialog';
import { ProposeNewPODialog } from './ProposeNewPODialog';

const fld = PurchaseOrderDetailsRow.Fields;

@Decorators.registerClass('Lanman2.Default.ProposeNewPOGrid')
export class ProposeNewPOGrid extends EntityGrid<PurchaseOrderDetailsRow, any> {
    protected getColumnsKey() { return PurchaseOrderDetailsColumns.columnsKey; }
    protected getDialogType() { return PurchaseOrderDetailsDialog; }
    protected getRowDefinition() { return PurchaseOrderDetailsRow; }
    protected getService() { return PurchaseOrderDetailsService.baseUrl; }

    protected getInitialTitle() { return 'Proposed New POs' }

    protected getQuickFilters() {
        let quickFilters = super.getQuickFilters();

        const currentDate = new Date();
        const month = String(currentDate.getMonth() + 1).padStart(2, '0');
        const year = String(currentDate.getFullYear());
        const formattedDate = `${month}/${year}`;

        let leaseExpiryDateShortFilter = quickFilters.find(x => x.field === fld.LeaseExpiryDateShort);
        leaseExpiryDateShortFilter && (leaseExpiryDateShortFilter.init = (w: StringEditor) => {
            w.value = formattedDate
        });

        return quickFilters.filter(x => x.field === fld.IsReOrderInfoGatheringComplete
            || x.field === fld.LeaseExpiryDateShort);
    }

    protected setCriteriaParameter() {
        super.setCriteriaParameter();

        const itemTypeDesktop = ItemTypesRow.getLookup().items.find(x => x.ItemTypeName === 'Desktop');
        const itemTypeLaptop = ItemTypesRow.getLookup().items.find(x => x.ItemTypeName === 'Laptop');

        if (itemTypeDesktop && itemTypeLaptop) {

            const itemTypeDesktopId = itemTypeDesktop.ItemTypeId;
            const itemTypeLaptopId = itemTypeLaptop.ItemTypeId;
            const desktopLaptopCriteria = Criteria.or([[fld.ItemTypeId], '=', itemTypeDesktopId], [[fld.ItemTypeId], '=', itemTypeLaptopId]);

            const criteria = this.view.params.Criteria;
            const serialNumberCriteria = [[fld.SerialNumber], '!=', ''];
            const isDiscoveryCompleteCriteria = [[fld.IsDiscoveryComplete], '=', true];
            const replacementTypeCriteria = [[fld.ReplacementType], '=', 'Order New'];

            this.view.params.Criteria = Criteria.and(
                criteria,
                serialNumberCriteria,
                isDiscoveryCompleteCriteria,
                replacementTypeCriteria,
                desktopLaptopCriteria
            );
        }
    }

    protected onClick(e: Event, row: number, cell: number) {

        e.preventDefault();


        let item = this.itemAt(row);
        let purchaseOrderDialog = new ProposeNewPODialog(item.PurchaseOrderId);
        this.initDialog(purchaseOrderDialog);
           
        (PurchaseOrderDetailsRow.getLookupAsync()).then(a => {

            let purchaseOrderDetails = a.items.filter(x => x.PurchaseOrderId === item.PurchaseOrderId);
            purchaseOrderDetails.forEach(x => x.SerialNumber = '');

            let purcaseOrderDetailForDesktopOrLaptop = purchaseOrderDetails.filter(x => x.ItemTypeName === 'Desktop' || x.ItemTypeName === 'Laptop')[0];

            if (!purcaseOrderDetailForDesktopOrLaptop.ReplacementPurchaseOrderId) {

                purchaseOrderDetails.splice(0, purchaseOrderDetails.length);

                (ItemsRow.getLookupAsync()).then(c => {

                    let newPODetail = deepClone(purcaseOrderDetailForDesktopOrLaptop);

                    let defaultItem = c.items.filter(i => i.IsDefault
                        && i.ItemTypeName === purcaseOrderDetailForDesktopOrLaptop.ItemTypeName)[0];

                    if (defaultItem) {
                        newPODetail.ItemId = defaultItem.ItemId;
                        newPODetail.ItemDescription = defaultItem.ItemDescription;
                        newPODetail.ItemName = defaultItem.ItemName;
                        purchaseOrderDetails.push(newPODetail);
                    }

                    purchaseOrderDialog.loadEntityAndOpenDialog(<PurchaseOrdersRow>{
                        AssignedOwnerEmployeeId: purcaseOrderDetailForDesktopOrLaptop.AssignedOwnerEmployeeId,
                        CostCenterId: purcaseOrderDetailForDesktopOrLaptop.CostCenterId,
                        ShippingAttention: purcaseOrderDetailForDesktopOrLaptop.ShippingAttention,
                        ShippingAddressesId: purcaseOrderDetailForDesktopOrLaptop.ShippingAddressesId,
                        ShippingAddressesAddress: purcaseOrderDetailForDesktopOrLaptop.ShippingAddressesAddress,
                        ShippingAddressesCityName: purcaseOrderDetailForDesktopOrLaptop.ShippingAddressesCityName,
                        ShippingAddressesStateName: purcaseOrderDetailForDesktopOrLaptop.ShippingAddressesStateName,
                        ShippingAddressesZIPCode: purcaseOrderDetailForDesktopOrLaptop.ShippingAddressesZIPCode,
                        VendorId: purcaseOrderDetailForDesktopOrLaptop.VendorId,

                        PurchaseOrderDetailsList: purchaseOrderDetails
                    });

                });
            }
            else
            {
                (PurchaseOrdersRow.getLookupAsync()).then(c => {
                    let replacementPurchaseOrder = c.itemById[purcaseOrderDetailForDesktopOrLaptop.ReplacementPurchaseOrderId];
                    let replacementPurchaseOrderDetails = a.items.filter(x => x.PurchaseOrderId === purcaseOrderDetailForDesktopOrLaptop.ReplacementPurchaseOrderId);

                    purchaseOrderDialog.loadEntityAndOpenDialog(<PurchaseOrdersRow>{
                        PurchaseOrderId: purcaseOrderDetailForDesktopOrLaptop.ReplacementPurchaseOrderId,
                        AssignedOwnerEmployeeId: replacementPurchaseOrder.AssignedOwnerEmployeeId,
                        CostCenterId: replacementPurchaseOrder.CostCenterId,
                        ShippingAttention: replacementPurchaseOrder.ShippingAttention,
                        ShippingAddressesId: replacementPurchaseOrder.ShippingAddressesId,
                        ShippingAddressesAddress: replacementPurchaseOrder.ShippingAddressesAddress,
                        ShippingAddressesCityName: replacementPurchaseOrder.ShippingAddressesCityName,
                        ShippingAddressesStateName: replacementPurchaseOrder.ShippingAddressesStateName,
                        ShippingAddressesZIPCode: replacementPurchaseOrder.ShippingAddressesZIPCode,
                        VendorId: replacementPurchaseOrder.VendorId,
                        IsReOrderInfoGatheringComplete: purcaseOrderDetailForDesktopOrLaptop.IsReOrderInfoGatheringComplete,

                        PurchaseOrderDetailsList: replacementPurchaseOrderDetails
                    });
                });
            }
        });
    }

    protected getButtons(): ToolButton[] {
        let buttons = super.getButtons();

        buttons.splice(indexOf(buttons, x => x.action == "add"), 1);

        return buttons;
    }

    protected getColumns() {
        let columns = super.getColumns();

        columns.splice(indexOf(columns, x => x.field == "IsDiscoveryComplete"), 1);
        columns.splice(indexOf(columns, x => x.field == "IsReplacementPOComplete"), 1);
        columns.splice(indexOf(columns, x => x.field == "ReturnDate"), 1);
        columns.splice(indexOf(columns, x => x.field == "ReturnEmailDate"), 1);
        columns.splice(indexOf(columns, x => x.field == "ReturnTrackingNumber"), 1);
        columns.splice(indexOf(columns, x => x.field == "ItemDisabledDate"), 1);
        columns.splice(indexOf(columns, x => x.field == "IsReturnComplete"), 1);


        return columns;
    }
}