﻿import { DepartmentsColumns, DepartmentsRow, DepartmentsService } from '@/ServerTypes/Default';
import { Decorators, EntityGrid } from '@serenity-is/corelib';
import { DepartmentsDialog } from './DepartmentsDialog';

@Decorators.registerClass('Lanman2.Default.DepartmentsGrid')
export class DepartmentsGrid extends EntityGrid<DepartmentsRow, any> {
    protected getColumnsKey() { return DepartmentsColumns.columnsKey; }
    protected getDialogType() { return DepartmentsDialog; }
    protected getRowDefinition() { return DepartmentsRow; }
    protected getService() { return DepartmentsService.baseUrl; }

}