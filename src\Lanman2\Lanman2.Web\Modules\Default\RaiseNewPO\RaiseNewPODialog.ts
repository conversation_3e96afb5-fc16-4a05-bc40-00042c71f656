import {  PurchaseOrdersRow, PurchaseOrdersService } from '@/ServerTypes/Default';
import { Decorators, indexOf } from '@serenity-is/corelib';
import { PurchaseOrdersDialog } from '../PurchaseOrders/PurchaseOrdersDialog';


@Decorators.registerClass('Lanman2.Default.RaiseNewPODialog')
export class RaiseNewPODialog extends PurchaseOrdersDialog {

    protected afterLoadEntity() {
        super.afterLoadEntity();

        this.hideUnhideElements();

        this.setIsReplacementPOCompleteValue();
        this.setPoCreatedDate();

        this.set_dialogTitle("Create New PO");

        this.enableDisablePOPDFButton();
    }

    getToolbarButtons() {
        let buttons = super.getToolbarButtons();

        buttons.splice(indexOf(buttons, x => x.title == "Delete"), 1);
        

        return buttons;
    }

    hideUnhideElements() {
        this.form.PurchaseOrderNotes.getGridField().toggle(false);
        this.form.PurchaseOrderEmailSentOnDate.getGridField().toggle(false);
        this.form.IsReOrderInfoGatheringComplete.getGridField().toggle(false);
        this.form.IsReplacementPOComplete.getGridField().toggle(true);
    }

    setIsReplacementPOCompleteValue() {
        if (this.oldPurchaseOrderId) {

            (PurchaseOrdersRow.getLookupAsync()).then(a => {
                let oldPurchaseOrder = a.itemById[this.oldPurchaseOrderId];
                this.form.IsReplacementPOComplete.value = oldPurchaseOrder.IsReplacementPOComplete;
            });
        }
    }

    setPoCreatedDate() {
        if (!this.form.PurchaseOrderCreatedDate.value)
            this.form.PurchaseOrderCreatedDate.valueAsDate = new Date();
    }

    onSaveSuccess(response) {
        super.onSaveSuccess(response);

        this.enableDisablePOPDFButton();
    }

    enableDisablePOPDFButton() {

        PurchaseOrdersService.Retrieve({
            EntityId: this.oldPurchaseOrderId
        }, response => {
            if (this.toolbar) {
                if (response.Entity.IsReplacementPOComplete) {

                    this.toolbar.findButton('export-pdf-button').show();

                }
                else {
                    let pdfButton = this.toolbar.findButton('export-pdf-button');
                    if (pdfButton)
                        pdfButton.hide();
                }
            }
        });

    }

}

 
