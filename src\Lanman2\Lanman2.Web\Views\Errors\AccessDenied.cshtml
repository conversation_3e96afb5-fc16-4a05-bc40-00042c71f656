@model string
@inject ITextLocalizer Localizer
@{
	ViewData["Title"] = Texts.Site.AccessDenied.PageTitle.ToString(Localizer);
	ViewData["PageId"] = "AccessDenied";
	Layout = User.IsLoggedIn() ?
		"~/Views/Shared/_Layout.cshtml" :
		"~/Views/Shared/_LayoutNoNavigation.cshtml";
}

<div class="page-content">
	<div class="message">
		<table>
			<caption>AccessDenied Error page</caption>
			<thead>
				<tr>
					<th scope="col"></th>
					<th scope="col"></th>
				</tr>
			</thead>
			<tbody>
				<tr>
					<td style="width: 200px;">
						<i class="fa fa-times-circle text-red" style="font-size: 180px;"></i>
					</td>
					<td>
						<h1>@Texts.Site.AccessDenied.PageTitle.ToString(Localizer)</h1>
						<h3>@(User.IsLoggedIn() ? Texts.Site.AccessDenied.LackPermissions.ToString(Localizer) : Texts.Site.AccessDenied.NotLoggedIn.ToString(Localizer))</h3>
						@if (Model != null)
						{
							<a class="redirect" href="/Account/Login?returnURL=@Model">
								@(User.IsLoggedIn() ?
															Texts.Site.AccessDenied.ClickToChangeUser.ToString(Localizer) : Texts.Site.AccessDenied.ClickToLogin.ToString(Localizer))
							</a>
						}
						else
						{
							<a class="redirect" href="/Account/Login">@Texts.Site.AccessDenied.ClickToLogin.ToString(Localizer)</a>
						}
						<div class="date-time">
							@(!User.IsLoggedIn() ? "" : (User?.Identity?.Name + " - "))@DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
						</div>
						<div class="clear"></div>
					</td>
				</tr>
			</tbody>
		</table>
	</div>
</div>