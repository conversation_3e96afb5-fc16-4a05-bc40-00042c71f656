﻿import { EmployeesColumns, EmployeesRow, EmployeesService } from '@/ServerTypes/Default';
import { Decorators, EntityGrid } from '@serenity-is/corelib';
import { EmployeesDialog } from './EmployeesDialog';

@Decorators.registerClass('Lanman2.Default.EmployeesGrid')
export class EmployeesGrid extends EntityGrid<EmployeesRow, any> {
    protected getColumnsKey() { return EmployeesColumns.columnsKey; }
    protected getDialogType() { return EmployeesDialog; }
    protected getRowDefinition() { return EmployeesRow; }
    protected getService() { return EmployeesService.baseUrl; }

}