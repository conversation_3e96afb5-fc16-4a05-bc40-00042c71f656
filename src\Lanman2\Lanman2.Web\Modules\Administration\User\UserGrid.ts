import { RoleRow, UserColumns, UserRow, UserService } from "@/ServerTypes/Administration";
import { Decorators, EntityGrid, Lookup, tryFirst  } from "@serenity-is/corelib";
import { UserDialog } from "./UserDialog";

@Decorators.registerClass()
export class UserGrid extends EntityGrid<UserRow, any> {
    protected getColumnsKey() { return UserColumns.columnsKey; }
    protected getDialogType() { return UserDialog; }
    protected getIdProperty() { return UserRow.idProperty; }
    protected getIsActiveProperty() { return UserRow.isActiveProperty; }
    protected getLocalTextPrefix() { return UserRow.localTextPrefix; }
    protected getService() { return UserService.baseUrl; }

    protected override getDefaultSortBy() {
        return [UserRow.Fields.Username];
    }

    protected override createIncludeDeletedButton() {
        // Disable "Include Deleted" button; method intentionally returns null
        return null;
    }

    protected override getColumns() {
        let columns = super.getColumns();

        let roles = tryFirst(columns, x => x.field == UserRow.Fields.Roles);
        if (roles) {
            let rolesLookup: Lookup<RoleRow>;
            RoleRow.getLookupAsync().then(lookup => {
                rolesLookup = lookup;
                this.slickGrid.invalidate();
            });

            roles.format = ctx => {
                if (!rolesLookup)
                    return `<i class="fa fa-spinner"></i>`;

                // Use optional chaining to avoid unnecessary OR and improve readability
                let roleList = (ctx.value || []).map(x => rolesLookup.itemById?.[x]?.RoleName || "");
                roleList.sort();
                return roleList.join(", ");
            };
        }

        return columns;
    }
}
